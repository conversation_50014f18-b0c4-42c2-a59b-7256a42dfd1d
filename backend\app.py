from flask import Flask, request, jsonify
from flask_socketio import <PERSON><PERSON><PERSON>, emit, join_room, leave_room
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import J<PERSON><PERSON>anager, jwt_required, create_access_token, get_jwt_identity
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///chat.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['JWT_SECRET_KEY'] = 'jwt-secret-string'
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)

# 初始化扩展
db = SQLAlchemy(app)
jwt = JWTManager(app)
CORS(app, origins="*")
socketio = SocketIO(app, cors_allowed_origins="*")

# 数据库模型
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_online = db.Column(db.Boolean, default=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'is_online': self.is_online,
            'created_at': self.created_at.isoformat()
        }

class Message(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    room = db.Column(db.String(100), default='general')
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    user = db.relationship('User', backref=db.backref('messages', lazy=True))

    def to_dict(self):
        return {
            'id': self.id,
            'content': self.content,
            'user_id': self.user_id,
            'username': self.user.username,
            'room': self.room,
            'timestamp': self.timestamp.isoformat()
        }

# API路由
@app.route('/api/register', methods=['POST'])
def register():
    data = request.get_json()
    
    if User.query.filter_by(username=data['username']).first():
        return jsonify({'message': '用户名已存在'}), 400
    
    if User.query.filter_by(email=data['email']).first():
        return jsonify({'message': '邮箱已被注册'}), 400
    
    user = User(username=data['username'], email=data['email'])
    user.set_password(data['password'])
    
    db.session.add(user)
    db.session.commit()
    
    access_token = create_access_token(identity=user.id)
    return jsonify({
        'access_token': access_token,
        'user': user.to_dict()
    }), 201

@app.route('/api/login', methods=['POST'])
def login():
    data = request.get_json()
    user = User.query.filter_by(username=data['username']).first()
    
    if user and user.check_password(data['password']):
        access_token = create_access_token(identity=user.id)
        user.is_online = True
        db.session.commit()
        
        return jsonify({
            'access_token': access_token,
            'user': user.to_dict()
        })
    
    return jsonify({'message': '用户名或密码错误'}), 401

@app.route('/api/logout', methods=['POST'])
@jwt_required()
def logout():
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    if user:
        user.is_online = False
        db.session.commit()
    
    return jsonify({'message': '登出成功'})

@app.route('/api/messages', methods=['GET'])
@jwt_required()
def get_messages():
    room = request.args.get('room', 'general')
    messages = Message.query.filter_by(room=room).order_by(Message.timestamp.desc()).limit(50).all()
    return jsonify([msg.to_dict() for msg in reversed(messages)])

@app.route('/api/users', methods=['GET'])
@jwt_required()
def get_users():
    users = User.query.filter_by(is_online=True).all()
    return jsonify([user.to_dict() for user in users])

# WebSocket事件处理
@socketio.on('connect')
@jwt_required()
def handle_connect():
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    if user:
        user.is_online = True
        db.session.commit()
        join_room('general')
        emit('user_joined', {'user': user.to_dict()}, room='general')

@socketio.on('disconnect')
@jwt_required()
def handle_disconnect():
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    if user:
        user.is_online = False
        db.session.commit()
        emit('user_left', {'user': user.to_dict()}, room='general')

@socketio.on('send_message')
@jwt_required()
def handle_message(data):
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    
    if user:
        message = Message(
            content=data['content'],
            user_id=user_id,
            room=data.get('room', 'general')
        )
        db.session.add(message)
        db.session.commit()
        
        emit('new_message', message.to_dict(), room=data.get('room', 'general'))

@socketio.on('join_room')
@jwt_required()
def handle_join_room(data):
    room = data['room']
    join_room(room)
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    emit('user_joined_room', {'user': user.to_dict(), 'room': room}, room=room)

@socketio.on('leave_room')
@jwt_required()
def handle_leave_room(data):
    room = data['room']
    leave_room(room)
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    emit('user_left_room', {'user': user.to_dict(), 'room': room}, room=room)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
