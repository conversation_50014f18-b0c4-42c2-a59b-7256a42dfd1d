from flask import Flask, request, jsonify
from flask_socketio import <PERSON><PERSON><PERSON>, emit, join_room, leave_room
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import J<PERSON><PERSON>anager, jwt_required, create_access_token, get_jwt_identity
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///chat.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['JWT_SECRET_KEY'] = 'jwt-secret-string'
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)

# 初始化扩展
db = SQLAlchemy(app)
jwt = JWTManager(app)
CORS(app, origins="*")
socketio = SocketIO(app, cors_allowed_origins="*")

# 数据库模型
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    avatar = db.Column(db.String(200), default='')
    bio = db.Column(db.Text, default='')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_seen = db.Column(db.DateTime, default=datetime.utcnow)
    is_online = db.Column(db.Boolean, default=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'avatar': self.avatar,
            'bio': self.bio,
            'is_online': self.is_online,
            'last_seen': self.last_seen.isoformat() if self.last_seen else None,
            'created_at': self.created_at.isoformat()
        }

# 好友关系表
class Friendship(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    friend_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, accepted, blocked
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    user = db.relationship('User', foreign_keys=[user_id], backref='sent_requests')
    friend = db.relationship('User', foreign_keys=[friend_id], backref='received_requests')

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'friend_id': self.friend_id,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'user': self.user.to_dict(),
            'friend': self.friend.to_dict()
        }

# 群组表
class Group(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, default='')
    avatar = db.Column(db.String(200), default='')
    creator_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_private = db.Column(db.Boolean, default=True)

    creator = db.relationship('User', backref='created_groups')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'avatar': self.avatar,
            'creator_id': self.creator_id,
            'created_at': self.created_at.isoformat(),
            'is_private': self.is_private,
            'creator': self.creator.to_dict()
        }

# 群组成员表
class GroupMember(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    group_id = db.Column(db.Integer, db.ForeignKey('group.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    role = db.Column(db.String(20), default='member')  # admin, member
    joined_at = db.Column(db.DateTime, default=datetime.utcnow)

    group = db.relationship('Group', backref='members')
    user = db.relationship('User', backref='group_memberships')

    def to_dict(self):
        return {
            'id': self.id,
            'group_id': self.group_id,
            'user_id': self.user_id,
            'role': self.role,
            'joined_at': self.joined_at.isoformat(),
            'user': self.user.to_dict()
        }

# 聊天会话表
class Conversation(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    type = db.Column(db.String(20), nullable=False)  # private, group
    group_id = db.Column(db.Integer, db.ForeignKey('group.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_message_at = db.Column(db.DateTime, default=datetime.utcnow)

    group = db.relationship('Group', backref='conversation')

    def to_dict(self):
        return {
            'id': self.id,
            'type': self.type,
            'group_id': self.group_id,
            'created_at': self.created_at.isoformat(),
            'last_message_at': self.last_message_at.isoformat()
        }

# 会话参与者表
class ConversationParticipant(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    conversation_id = db.Column(db.Integer, db.ForeignKey('conversation.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    joined_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_read_at = db.Column(db.DateTime, default=datetime.utcnow)

    conversation = db.relationship('Conversation', backref='participants')
    user = db.relationship('User', backref='conversations')

# 消息表
class Message(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    conversation_id = db.Column(db.Integer, db.ForeignKey('conversation.id'), nullable=False)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    content = db.Column(db.Text, nullable=False)
    message_type = db.Column(db.String(20), default='text')  # text, image, file
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    edited_at = db.Column(db.DateTime, nullable=True)
    is_deleted = db.Column(db.Boolean, default=False)

    conversation = db.relationship('Conversation', backref='messages')
    sender = db.relationship('User', backref='sent_messages')

    def to_dict(self):
        return {
            'id': self.id,
            'conversation_id': self.conversation_id,
            'sender_id': self.sender_id,
            'content': self.content,
            'message_type': self.message_type,
            'timestamp': self.timestamp.isoformat(),
            'edited_at': self.edited_at.isoformat() if self.edited_at else None,
            'is_deleted': self.is_deleted,
            'sender': self.sender.to_dict()
        }

# API路由
@app.route('/api/register', methods=['POST'])
def register():
    data = request.get_json()

    if User.query.filter_by(username=data['username']).first():
        return jsonify({'message': '用户名已存在'}), 400

    if User.query.filter_by(email=data['email']).first():
        return jsonify({'message': '邮箱已被注册'}), 400

    user = User(username=data['username'], email=data['email'])
    user.set_password(data['password'])

    db.session.add(user)
    db.session.commit()

    access_token = create_access_token(identity=user.id)
    return jsonify({
        'access_token': access_token,
        'user': user.to_dict()
    }), 201

@app.route('/api/login', methods=['POST'])
def login():
    data = request.get_json()
    user = User.query.filter_by(username=data['username']).first()

    if user and user.check_password(data['password']):
        access_token = create_access_token(identity=user.id)
        user.is_online = True
        db.session.commit()

        return jsonify({
            'access_token': access_token,
            'user': user.to_dict()
        })

    return jsonify({'message': '用户名或密码错误'}), 401

@app.route('/api/logout', methods=['POST'])
@jwt_required()
def logout():
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    if user:
        user.is_online = False
        db.session.commit()

    return jsonify({'message': '登出成功'})

# 好友相关API
@app.route('/api/friends', methods=['GET'])
@jwt_required()
def get_friends():
    user_id = get_jwt_identity()
    friends = db.session.query(Friendship).filter(
        ((Friendship.user_id == user_id) | (Friendship.friend_id == user_id)) &
        (Friendship.status == 'accepted')
    ).all()

    friend_list = []
    for friendship in friends:
        friend = friendship.friend if friendship.user_id == user_id else friendship.user
        friend_list.append(friend.to_dict())

    return jsonify(friend_list)

@app.route('/api/friends/requests', methods=['GET'])
@jwt_required()
def get_friend_requests():
    user_id = get_jwt_identity()
    requests = Friendship.query.filter_by(friend_id=user_id, status='pending').all()
    return jsonify([req.to_dict() for req in requests])

@app.route('/api/friends/add', methods=['POST'])
@jwt_required()
def add_friend():
    user_id = get_jwt_identity()
    data = request.get_json()
    friend_username = data.get('username')

    friend = User.query.filter_by(username=friend_username).first()
    if not friend:
        return jsonify({'message': '用户不存在'}), 404

    if friend.id == user_id:
        return jsonify({'message': '不能添加自己为好友'}), 400

    # 检查是否已经是好友或已发送请求
    existing = Friendship.query.filter(
        ((Friendship.user_id == user_id) & (Friendship.friend_id == friend.id)) |
        ((Friendship.user_id == friend.id) & (Friendship.friend_id == user_id))
    ).first()

    if existing:
        if existing.status == 'accepted':
            return jsonify({'message': '已经是好友了'}), 400
        elif existing.status == 'pending':
            return jsonify({'message': '好友请求已发送'}), 400

    # 创建好友请求
    friendship = Friendship(user_id=user_id, friend_id=friend.id, status='pending')
    db.session.add(friendship)
    db.session.commit()

    return jsonify({'message': '好友请求已发送'})

@app.route('/api/friends/accept/<int:request_id>', methods=['POST'])
@jwt_required()
def accept_friend_request(request_id):
    user_id = get_jwt_identity()
    friendship = Friendship.query.filter_by(id=request_id, friend_id=user_id, status='pending').first()

    if not friendship:
        return jsonify({'message': '请求不存在'}), 404

    friendship.status = 'accepted'
    db.session.commit()

    return jsonify({'message': '好友请求已接受'})

@app.route('/api/friends/reject/<int:request_id>', methods=['POST'])
@jwt_required()
def reject_friend_request(request_id):
    user_id = get_jwt_identity()
    friendship = Friendship.query.filter_by(id=request_id, friend_id=user_id, status='pending').first()

    if not friendship:
        return jsonify({'message': '请求不存在'}), 404

    db.session.delete(friendship)
    db.session.commit()

    return jsonify({'message': '好友请求已拒绝'})

# 群组相关API
@app.route('/api/groups', methods=['GET'])
@jwt_required()
def get_user_groups():
    user_id = get_jwt_identity()
    memberships = GroupMember.query.filter_by(user_id=user_id).all()
    groups = [membership.group.to_dict() for membership in memberships]
    return jsonify(groups)

@app.route('/api/groups', methods=['POST'])
@jwt_required()
def create_group():
    user_id = get_jwt_identity()
    data = request.get_json()

    group = Group(
        name=data['name'],
        description=data.get('description', ''),
        creator_id=user_id
    )
    db.session.add(group)
    db.session.flush()  # 获取group.id

    # 创建者自动成为管理员
    membership = GroupMember(group_id=group.id, user_id=user_id, role='admin')
    db.session.add(membership)

    # 创建群组会话
    conversation = Conversation(type='group', group_id=group.id)
    db.session.add(conversation)
    db.session.flush()

    # 添加创建者到会话
    participant = ConversationParticipant(conversation_id=conversation.id, user_id=user_id)
    db.session.add(participant)

    db.session.commit()

    return jsonify(group.to_dict()), 201

@app.route('/api/groups/<int:group_id>/invite', methods=['POST'])
@jwt_required()
def invite_to_group(group_id):
    user_id = get_jwt_identity()
    data = request.get_json()
    friend_username = data.get('username')

    # 检查是否是群组成员
    membership = GroupMember.query.filter_by(group_id=group_id, user_id=user_id).first()
    if not membership:
        return jsonify({'message': '你不是群组成员'}), 403

    # 查找要邀请的用户
    friend = User.query.filter_by(username=friend_username).first()
    if not friend:
        return jsonify({'message': '用户不存在'}), 404

    # 检查是否已经是群组成员
    existing_member = GroupMember.query.filter_by(group_id=group_id, user_id=friend.id).first()
    if existing_member:
        return jsonify({'message': '用户已经是群组成员'}), 400

    # 添加到群组
    new_membership = GroupMember(group_id=group_id, user_id=friend.id)
    db.session.add(new_membership)

    # 添加到群组会话
    group = Group.query.get(group_id)
    conversation = group.conversation
    participant = ConversationParticipant(conversation_id=conversation.id, user_id=friend.id)
    db.session.add(participant)

    db.session.commit()

    return jsonify({'message': f'{friend.username} 已加入群组'})

# 会话相关API
@app.route('/api/conversations', methods=['GET'])
@jwt_required()
def get_conversations():
    user_id = get_jwt_identity()
    participants = ConversationParticipant.query.filter_by(user_id=user_id).all()

    conversations = []
    for participant in participants:
        conv = participant.conversation
        conv_data = conv.to_dict()

        if conv.type == 'private':
            # 获取对方用户信息
            other_participant = ConversationParticipant.query.filter(
                (ConversationParticipant.conversation_id == conv.id) &
                (ConversationParticipant.user_id != user_id)
            ).first()
            if other_participant:
                conv_data['other_user'] = other_participant.user.to_dict()
        elif conv.type == 'group':
            conv_data['group'] = conv.group.to_dict()

        # 获取最后一条消息
        last_message = Message.query.filter_by(conversation_id=conv.id).order_by(Message.timestamp.desc()).first()
        if last_message:
            conv_data['last_message'] = last_message.to_dict()

        conversations.append(conv_data)

    return jsonify(conversations)

@app.route('/api/conversations/<int:conversation_id>/messages', methods=['GET'])
@jwt_required()
def get_conversation_messages(conversation_id):
    user_id = get_jwt_identity()

    # 检查用户是否是会话参与者
    participant = ConversationParticipant.query.filter_by(
        conversation_id=conversation_id, user_id=user_id
    ).first()
    if not participant:
        return jsonify({'message': '无权访问此会话'}), 403

    messages = Message.query.filter_by(
        conversation_id=conversation_id, is_deleted=False
    ).order_by(Message.timestamp.desc()).limit(50).all()

    return jsonify([msg.to_dict() for msg in reversed(messages)])

@app.route('/api/conversations/private', methods=['POST'])
@jwt_required()
def create_private_conversation():
    user_id = get_jwt_identity()
    data = request.get_json()
    friend_id = data.get('friend_id')

    # 检查是否已存在私聊会话
    existing_conv = db.session.query(Conversation).join(ConversationParticipant).filter(
        Conversation.type == 'private'
    ).filter(
        ConversationParticipant.user_id.in_([user_id, friend_id])
    ).group_by(Conversation.id).having(
        db.func.count(ConversationParticipant.user_id) == 2
    ).first()

    if existing_conv:
        return jsonify(existing_conv.to_dict())

    # 创建新的私聊会话
    conversation = Conversation(type='private')
    db.session.add(conversation)
    db.session.flush()

    # 添加参与者
    participant1 = ConversationParticipant(conversation_id=conversation.id, user_id=user_id)
    participant2 = ConversationParticipant(conversation_id=conversation.id, user_id=friend_id)
    db.session.add(participant1)
    db.session.add(participant2)

    db.session.commit()

    return jsonify(conversation.to_dict()), 201

# 管理界面路由
@app.route('/admin')
def admin_dashboard():
    return '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>GlesChat 管理后台</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                background: #f5f5f5;
                color: #333;
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                text-align: center;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .container {
                max-width: 1200px;
                margin: 20px auto;
                padding: 0 20px;
            }
            .stats {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }
            .stat-card {
                background: white;
                padding: 25px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                text-align: center;
            }
            .stat-number {
                font-size: 2.5rem;
                font-weight: bold;
                color: #667eea;
                margin-bottom: 10px;
            }
            .stat-label {
                color: #666;
                font-size: 1.1rem;
            }
            .section {
                background: white;
                margin-bottom: 20px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                overflow: hidden;
            }
            .section-header {
                background: #f8f9fa;
                padding: 20px;
                border-bottom: 1px solid #e9ecef;
                font-size: 1.2rem;
                font-weight: 600;
            }
            .section-content {
                padding: 20px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
            }
            th, td {
                padding: 12px;
                text-align: left;
                border-bottom: 1px solid #e9ecef;
            }
            th {
                background: #f8f9fa;
                font-weight: 600;
            }
            .status-online {
                color: #28a745;
                font-weight: 600;
            }
            .status-offline {
                color: #6c757d;
            }
            .btn {
                background: #dc3545;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 0.9rem;
            }
            .btn:hover {
                background: #c82333;
            }
            .refresh-btn {
                background: #667eea;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                margin-bottom: 20px;
            }
            .refresh-btn:hover {
                background: #5a6fd8;
            }
            @media (max-width: 768px) {
                .container { padding: 0 10px; }
                .stats { grid-template-columns: 1fr; }
                table { font-size: 0.9rem; }
                th, td { padding: 8px; }
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🚀 GlesChat 管理后台</h1>
            <p>实时监控和管理聊天系统</p>
        </div>

        <div class="container">
            <button class="refresh-btn" onclick="location.reload()">🔄 刷新数据</button>

            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="total-users">-</div>
                    <div class="stat-label">总用户数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="online-users">-</div>
                    <div class="stat-label">在线用户</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="total-messages">-</div>
                    <div class="stat-label">总消息数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="today-messages">-</div>
                    <div class="stat-label">今日消息</div>
                </div>
            </div>

            <div class="section">
                <div class="section-header">👥 用户管理</div>
                <div class="section-content">
                    <table id="users-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户名</th>
                                <th>邮箱</th>
                                <th>状态</th>
                                <th>注册时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>

            <div class="section">
                <div class="section-header">💬 最近消息</div>
                <div class="section-content">
                    <table id="messages-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户</th>
                                <th>内容</th>
                                <th>房间</th>
                                <th>时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>

        <script>
            // 加载统计数据
            async function loadStats() {
                try {
                    const response = await fetch('/admin/api/stats');
                    const stats = await response.json();

                    document.getElementById('total-users').textContent = stats.total_users;
                    document.getElementById('online-users').textContent = stats.online_users;
                    document.getElementById('total-messages').textContent = stats.total_messages;
                    document.getElementById('today-messages').textContent = stats.today_messages;
                } catch (error) {
                    console.error('加载统计数据失败:', error);
                }
            }

            // 加载用户列表
            async function loadUsers() {
                try {
                    const response = await fetch('/admin/api/users');
                    const users = await response.json();

                    const tbody = document.querySelector('#users-table tbody');
                    tbody.innerHTML = '';

                    users.forEach(user => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${user.id}</td>
                            <td>${user.username}</td>
                            <td>${user.email}</td>
                            <td class="${user.is_online ? 'status-online' : 'status-offline'}">
                                ${user.is_online ? '在线' : '离线'}
                            </td>
                            <td>${new Date(user.created_at).toLocaleString('zh-CN')}</td>
                            <td>
                                <button class="btn" onclick="deleteUser(${user.id})">删除</button>
                            </td>
                        `;
                        tbody.appendChild(row);
                    });
                } catch (error) {
                    console.error('加载用户列表失败:', error);
                }
            }

            // 加载消息列表
            async function loadMessages() {
                try {
                    const response = await fetch('/admin/api/messages');
                    const messages = await response.json();

                    const tbody = document.querySelector('#messages-table tbody');
                    tbody.innerHTML = '';

                    messages.forEach(message => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${message.id}</td>
                            <td>${message.username}</td>
                            <td>${message.content.length > 50 ? message.content.substring(0, 50) + '...' : message.content}</td>
                            <td>${message.room}</td>
                            <td>${new Date(message.timestamp).toLocaleString('zh-CN')}</td>
                            <td>
                                <button class="btn" onclick="deleteMessage(${message.id})">删除</button>
                            </td>
                        `;
                        tbody.appendChild(row);
                    });
                } catch (error) {
                    console.error('加载消息列表失败:', error);
                }
            }

            // 删除用户
            async function deleteUser(userId) {
                if (confirm('确定要删除这个用户吗？')) {
                    try {
                        const response = await fetch(`/admin/api/users/${userId}`, {
                            method: 'DELETE'
                        });
                        if (response.ok) {
                            alert('用户删除成功');
                            loadUsers();
                            loadStats();
                        } else {
                            alert('删除失败');
                        }
                    } catch (error) {
                        console.error('删除用户失败:', error);
                        alert('删除失败');
                    }
                }
            }

            // 删除消息
            async function deleteMessage(messageId) {
                if (confirm('确定要删除这条消息吗？')) {
                    try {
                        const response = await fetch(`/admin/api/messages/${messageId}`, {
                            method: 'DELETE'
                        });
                        if (response.ok) {
                            alert('消息删除成功');
                            loadMessages();
                            loadStats();
                        } else {
                            alert('删除失败');
                        }
                    } catch (error) {
                        console.error('删除消息失败:', error);
                        alert('删除失败');
                    }
                }
            }

            // 页面加载时初始化数据
            document.addEventListener('DOMContentLoaded', function() {
                loadStats();
                loadUsers();
                loadMessages();

                // 每30秒自动刷新数据
                setInterval(() => {
                    loadStats();
                    loadUsers();
                }, 30000);
            });
        </script>
    </body>
    </html>
    '''

# 管理界面API
@app.route('/admin/api/stats')
def admin_stats():
    total_users = User.query.count()
    online_users = User.query.filter_by(is_online=True).count()
    total_messages = Message.query.count()

    # 今日消息数
    today = datetime.now().date()
    today_start = datetime.combine(today, datetime.min.time())
    today_messages = Message.query.filter(Message.timestamp >= today_start).count()

    return jsonify({
        'total_users': total_users,
        'online_users': online_users,
        'total_messages': total_messages,
        'today_messages': today_messages
    })

@app.route('/admin/api/users')
def admin_users():
    users = User.query.order_by(User.created_at.desc()).all()
    return jsonify([user.to_dict() for user in users])

@app.route('/admin/api/messages')
def admin_messages():
    messages = Message.query.order_by(Message.timestamp.desc()).limit(100).all()
    return jsonify([msg.to_dict() for msg in messages])

@app.route('/admin/api/users/<int:user_id>', methods=['DELETE'])
def admin_delete_user(user_id):
    user = User.query.get_or_404(user_id)

    # 删除用户的所有消息
    Message.query.filter_by(user_id=user_id).delete()

    # 删除用户
    db.session.delete(user)
    db.session.commit()

    return jsonify({'message': '用户删除成功'})

@app.route('/admin/api/messages/<int:message_id>', methods=['DELETE'])
def admin_delete_message(message_id):
    message = Message.query.get_or_404(message_id)
    db.session.delete(message)
    db.session.commit()

    return jsonify({'message': '消息删除成功'})

# WebSocket事件处理
@socketio.on('connect')
def handle_connect(auth):
    try:
        # 从auth参数中获取token
        token = auth.get('token') if auth else None
        if not token:
            return False

        # 验证JWT token
        from flask_jwt_extended import decode_token
        decoded_token = decode_token(token)
        user_id = decoded_token['sub']

        user = User.query.get(user_id)
        if not user:
            return False

        user.is_online = True
        user.last_seen = datetime.now()
        db.session.commit()

        # 加入用户的所有会话房间
        participants = ConversationParticipant.query.filter_by(user_id=user_id).all()
        for participant in participants:
            join_room(f'conversation_{participant.conversation_id}')

        # 加入用户个人房间
        join_room(f'user_{user_id}')

        # 通知好友用户上线
        friends = db.session.query(Friendship).filter(
            ((Friendship.user_id == user_id) | (Friendship.friend_id == user_id)) &
            (Friendship.status == 'accepted')
        ).all()

        for friendship in friends:
            friend_id = friendship.friend_id if friendship.user_id == user_id else friendship.user_id
            emit('friend_online', {'user': user.to_dict()}, room=f'user_{friend_id}')

        return True
    except Exception as e:
        print(f"Socket连接认证失败: {e}")
        return False

@socketio.on('disconnect')
def handle_disconnect():
    # 在disconnect事件中，我们无法获取认证信息
    # 这里可以通过session或其他方式来处理
    pass

@socketio.on('send_message')
def handle_send_message(data):
    try:
        token = data.get('token')
        if not token:
            emit('error', {'message': '未提供认证token'})
            return

        from flask_jwt_extended import decode_token
        decoded_token = decode_token(token)
        user_id = decoded_token['sub']

        conversation_id = data.get('conversation_id')
        content = data.get('content')

        # 验证用户是否是会话参与者
        participant = ConversationParticipant.query.filter_by(
            conversation_id=conversation_id, user_id=user_id
        ).first()

        if not participant:
            emit('error', {'message': '无权发送消息到此会话'})
            return

        # 创建消息
        message = Message(
            conversation_id=conversation_id,
            sender_id=user_id,
            content=content
        )
        db.session.add(message)

        # 更新会话最后消息时间
        conversation = Conversation.query.get(conversation_id)
        conversation.last_message_at = datetime.now()

        db.session.commit()

        # 发送消息到会话房间
        emit('new_message', message.to_dict(), room=f'conversation_{conversation_id}')
    except Exception as e:
        emit('error', {'message': f'发送消息失败: {str(e)}'})

@socketio.on('join_conversation')
def handle_join_conversation(data):
    try:
        token = data.get('token')
        if not token:
            return

        from flask_jwt_extended import decode_token
        decoded_token = decode_token(token)
        user_id = decoded_token['sub']

        conversation_id = data.get('conversation_id')

        # 验证用户是否是会话参与者
        participant = ConversationParticipant.query.filter_by(
            conversation_id=conversation_id, user_id=user_id
        ).first()

        if participant:
            join_room(f'conversation_{conversation_id}')
            # 更新最后阅读时间
            participant.last_read_at = datetime.now()
            db.session.commit()
    except Exception as e:
        print(f"加入会话失败: {e}")

@socketio.on('leave_conversation')
def handle_leave_conversation(data):
    conversation_id = data.get('conversation_id')
    leave_room(f'conversation_{conversation_id}')

@socketio.on('typing')
def handle_typing(data):
    try:
        token = data.get('token')
        if not token:
            return

        from flask_jwt_extended import decode_token
        decoded_token = decode_token(token)
        user_id = decoded_token['sub']

        conversation_id = data.get('conversation_id')
        is_typing = data.get('is_typing', False)

        user = User.query.get(user_id)
        emit('user_typing', {
            'user': user.to_dict(),
            'is_typing': is_typing
        }, room=f'conversation_{conversation_id}', include_self=False)
    except Exception as e:
        print(f"处理打字状态失败: {e}")

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
