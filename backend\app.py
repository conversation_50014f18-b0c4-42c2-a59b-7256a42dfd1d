from flask import Flask, request, jsonify
from flask_socketio import <PERSON><PERSON><PERSON>, emit, join_room, leave_room
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import J<PERSON><PERSON>anager, jwt_required, create_access_token, get_jwt_identity
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///chat.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['JWT_SECRET_KEY'] = 'jwt-secret-string'
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)

# 初始化扩展
db = SQLAlchemy(app)
jwt = JWTManager(app)
CORS(app, origins="*")
socketio = SocketIO(app, cors_allowed_origins="*")

# 数据库模型
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_online = db.Column(db.Boolean, default=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'is_online': self.is_online,
            'created_at': self.created_at.isoformat()
        }

class Message(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    room = db.Column(db.String(100), default='general')
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    user = db.relationship('User', backref=db.backref('messages', lazy=True))

    def to_dict(self):
        return {
            'id': self.id,
            'content': self.content,
            'user_id': self.user_id,
            'username': self.user.username,
            'room': self.room,
            'timestamp': self.timestamp.isoformat()
        }

# API路由
@app.route('/api/register', methods=['POST'])
def register():
    data = request.get_json()

    if User.query.filter_by(username=data['username']).first():
        return jsonify({'message': '用户名已存在'}), 400

    if User.query.filter_by(email=data['email']).first():
        return jsonify({'message': '邮箱已被注册'}), 400

    user = User(username=data['username'], email=data['email'])
    user.set_password(data['password'])

    db.session.add(user)
    db.session.commit()

    access_token = create_access_token(identity=user.id)
    return jsonify({
        'access_token': access_token,
        'user': user.to_dict()
    }), 201

@app.route('/api/login', methods=['POST'])
def login():
    data = request.get_json()
    user = User.query.filter_by(username=data['username']).first()

    if user and user.check_password(data['password']):
        access_token = create_access_token(identity=user.id)
        user.is_online = True
        db.session.commit()

        return jsonify({
            'access_token': access_token,
            'user': user.to_dict()
        })

    return jsonify({'message': '用户名或密码错误'}), 401

@app.route('/api/logout', methods=['POST'])
@jwt_required()
def logout():
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    if user:
        user.is_online = False
        db.session.commit()

    return jsonify({'message': '登出成功'})

@app.route('/api/messages', methods=['GET'])
@jwt_required()
def get_messages():
    room = request.args.get('room', 'general')
    messages = Message.query.filter_by(room=room).order_by(Message.timestamp.desc()).limit(50).all()
    return jsonify([msg.to_dict() for msg in reversed(messages)])

@app.route('/api/users', methods=['GET'])
@jwt_required()
def get_users():
    users = User.query.filter_by(is_online=True).all()
    return jsonify([user.to_dict() for user in users])

# 管理界面路由
@app.route('/admin')
def admin_dashboard():
    return '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>GlesChat 管理后台</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                background: #f5f5f5;
                color: #333;
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                text-align: center;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .container {
                max-width: 1200px;
                margin: 20px auto;
                padding: 0 20px;
            }
            .stats {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }
            .stat-card {
                background: white;
                padding: 25px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                text-align: center;
            }
            .stat-number {
                font-size: 2.5rem;
                font-weight: bold;
                color: #667eea;
                margin-bottom: 10px;
            }
            .stat-label {
                color: #666;
                font-size: 1.1rem;
            }
            .section {
                background: white;
                margin-bottom: 20px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                overflow: hidden;
            }
            .section-header {
                background: #f8f9fa;
                padding: 20px;
                border-bottom: 1px solid #e9ecef;
                font-size: 1.2rem;
                font-weight: 600;
            }
            .section-content {
                padding: 20px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
            }
            th, td {
                padding: 12px;
                text-align: left;
                border-bottom: 1px solid #e9ecef;
            }
            th {
                background: #f8f9fa;
                font-weight: 600;
            }
            .status-online {
                color: #28a745;
                font-weight: 600;
            }
            .status-offline {
                color: #6c757d;
            }
            .btn {
                background: #dc3545;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 0.9rem;
            }
            .btn:hover {
                background: #c82333;
            }
            .refresh-btn {
                background: #667eea;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                margin-bottom: 20px;
            }
            .refresh-btn:hover {
                background: #5a6fd8;
            }
            @media (max-width: 768px) {
                .container { padding: 0 10px; }
                .stats { grid-template-columns: 1fr; }
                table { font-size: 0.9rem; }
                th, td { padding: 8px; }
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🚀 GlesChat 管理后台</h1>
            <p>实时监控和管理聊天系统</p>
        </div>

        <div class="container">
            <button class="refresh-btn" onclick="location.reload()">🔄 刷新数据</button>

            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="total-users">-</div>
                    <div class="stat-label">总用户数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="online-users">-</div>
                    <div class="stat-label">在线用户</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="total-messages">-</div>
                    <div class="stat-label">总消息数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="today-messages">-</div>
                    <div class="stat-label">今日消息</div>
                </div>
            </div>

            <div class="section">
                <div class="section-header">👥 用户管理</div>
                <div class="section-content">
                    <table id="users-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户名</th>
                                <th>邮箱</th>
                                <th>状态</th>
                                <th>注册时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>

            <div class="section">
                <div class="section-header">💬 最近消息</div>
                <div class="section-content">
                    <table id="messages-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户</th>
                                <th>内容</th>
                                <th>房间</th>
                                <th>时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>

        <script>
            // 加载统计数据
            async function loadStats() {
                try {
                    const response = await fetch('/admin/api/stats');
                    const stats = await response.json();

                    document.getElementById('total-users').textContent = stats.total_users;
                    document.getElementById('online-users').textContent = stats.online_users;
                    document.getElementById('total-messages').textContent = stats.total_messages;
                    document.getElementById('today-messages').textContent = stats.today_messages;
                } catch (error) {
                    console.error('加载统计数据失败:', error);
                }
            }

            // 加载用户列表
            async function loadUsers() {
                try {
                    const response = await fetch('/admin/api/users');
                    const users = await response.json();

                    const tbody = document.querySelector('#users-table tbody');
                    tbody.innerHTML = '';

                    users.forEach(user => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${user.id}</td>
                            <td>${user.username}</td>
                            <td>${user.email}</td>
                            <td class="${user.is_online ? 'status-online' : 'status-offline'}">
                                ${user.is_online ? '在线' : '离线'}
                            </td>
                            <td>${new Date(user.created_at).toLocaleString('zh-CN')}</td>
                            <td>
                                <button class="btn" onclick="deleteUser(${user.id})">删除</button>
                            </td>
                        `;
                        tbody.appendChild(row);
                    });
                } catch (error) {
                    console.error('加载用户列表失败:', error);
                }
            }

            // 加载消息列表
            async function loadMessages() {
                try {
                    const response = await fetch('/admin/api/messages');
                    const messages = await response.json();

                    const tbody = document.querySelector('#messages-table tbody');
                    tbody.innerHTML = '';

                    messages.forEach(message => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${message.id}</td>
                            <td>${message.username}</td>
                            <td>${message.content.length > 50 ? message.content.substring(0, 50) + '...' : message.content}</td>
                            <td>${message.room}</td>
                            <td>${new Date(message.timestamp).toLocaleString('zh-CN')}</td>
                            <td>
                                <button class="btn" onclick="deleteMessage(${message.id})">删除</button>
                            </td>
                        `;
                        tbody.appendChild(row);
                    });
                } catch (error) {
                    console.error('加载消息列表失败:', error);
                }
            }

            // 删除用户
            async function deleteUser(userId) {
                if (confirm('确定要删除这个用户吗？')) {
                    try {
                        const response = await fetch(`/admin/api/users/${userId}`, {
                            method: 'DELETE'
                        });
                        if (response.ok) {
                            alert('用户删除成功');
                            loadUsers();
                            loadStats();
                        } else {
                            alert('删除失败');
                        }
                    } catch (error) {
                        console.error('删除用户失败:', error);
                        alert('删除失败');
                    }
                }
            }

            // 删除消息
            async function deleteMessage(messageId) {
                if (confirm('确定要删除这条消息吗？')) {
                    try {
                        const response = await fetch(`/admin/api/messages/${messageId}`, {
                            method: 'DELETE'
                        });
                        if (response.ok) {
                            alert('消息删除成功');
                            loadMessages();
                            loadStats();
                        } else {
                            alert('删除失败');
                        }
                    } catch (error) {
                        console.error('删除消息失败:', error);
                        alert('删除失败');
                    }
                }
            }

            // 页面加载时初始化数据
            document.addEventListener('DOMContentLoaded', function() {
                loadStats();
                loadUsers();
                loadMessages();

                // 每30秒自动刷新数据
                setInterval(() => {
                    loadStats();
                    loadUsers();
                }, 30000);
            });
        </script>
    </body>
    </html>
    '''

# 管理界面API
@app.route('/admin/api/stats')
def admin_stats():
    total_users = User.query.count()
    online_users = User.query.filter_by(is_online=True).count()
    total_messages = Message.query.count()

    # 今日消息数
    today = datetime.now().date()
    today_start = datetime.combine(today, datetime.min.time())
    today_messages = Message.query.filter(Message.timestamp >= today_start).count()

    return jsonify({
        'total_users': total_users,
        'online_users': online_users,
        'total_messages': total_messages,
        'today_messages': today_messages
    })

@app.route('/admin/api/users')
def admin_users():
    users = User.query.order_by(User.created_at.desc()).all()
    return jsonify([user.to_dict() for user in users])

@app.route('/admin/api/messages')
def admin_messages():
    messages = Message.query.order_by(Message.timestamp.desc()).limit(100).all()
    return jsonify([msg.to_dict() for msg in messages])

@app.route('/admin/api/users/<int:user_id>', methods=['DELETE'])
def admin_delete_user(user_id):
    user = User.query.get_or_404(user_id)

    # 删除用户的所有消息
    Message.query.filter_by(user_id=user_id).delete()

    # 删除用户
    db.session.delete(user)
    db.session.commit()

    return jsonify({'message': '用户删除成功'})

@app.route('/admin/api/messages/<int:message_id>', methods=['DELETE'])
def admin_delete_message(message_id):
    message = Message.query.get_or_404(message_id)
    db.session.delete(message)
    db.session.commit()

    return jsonify({'message': '消息删除成功'})

# WebSocket事件处理
@socketio.on('connect')
@jwt_required()
def handle_connect():
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    if user:
        user.is_online = True
        db.session.commit()
        join_room('general')
        emit('user_joined', {'user': user.to_dict()}, room='general')

@socketio.on('disconnect')
@jwt_required()
def handle_disconnect():
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    if user:
        user.is_online = False
        db.session.commit()
        emit('user_left', {'user': user.to_dict()}, room='general')

@socketio.on('send_message')
@jwt_required()
def handle_message(data):
    user_id = get_jwt_identity()
    user = User.query.get(user_id)

    if user:
        message = Message(
            content=data['content'],
            user_id=user_id,
            room=data.get('room', 'general')
        )
        db.session.add(message)
        db.session.commit()

        emit('new_message', message.to_dict(), room=data.get('room', 'general'))

@socketio.on('join_room')
@jwt_required()
def handle_join_room(data):
    room = data['room']
    join_room(room)
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    emit('user_joined_room', {'user': user.to_dict(), 'room': room}, room=room)

@socketio.on('leave_room')
@jwt_required()
def handle_leave_room(data):
    room = data['room']
    leave_room(room)
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    emit('user_left_room', {'user': user.to_dict(), 'room': room}, room=room)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
