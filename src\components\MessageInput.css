.message-input-form {
  width: 100%;
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  background: #f8f9fa;
  border-radius: 25px;
  padding: 8px 8px 8px 20px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.input-container:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.message-textarea {
  flex: 1;
  border: none;
  background: transparent;
  resize: none;
  outline: none;
  font-size: 16px;
  line-height: 1.4;
  padding: 8px 0;
  min-height: 20px;
  max-height: 120px;
  font-family: inherit;
}

.message-textarea::placeholder {
  color: #6c757d;
}

.send-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.send-button svg {
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

.send-button:hover:not(:disabled) svg {
  transform: rotate(15deg);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .input-container {
    padding: 6px 6px 6px 16px;
    gap: 10px;
  }
  
  .message-textarea {
    font-size: 16px; /* 防止iOS缩放 */
    padding: 6px 0;
  }
  
  .send-button {
    width: 36px;
    height: 36px;
  }
  
  .send-button svg {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 480px) {
  .input-container {
    padding: 5px 5px 5px 14px;
    gap: 8px;
  }
  
  .message-textarea {
    padding: 5px 0;
  }
  
  .send-button {
    width: 34px;
    height: 34px;
  }
  
  .send-button svg {
    width: 16px;
    height: 16px;
  }
}
