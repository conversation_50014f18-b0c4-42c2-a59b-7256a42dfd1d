.user-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.user-list-header {
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.user-list-header h3 {
  margin: 0;
  color: #495057;
  font-size: 1.1rem;
  font-weight: 600;
}

.user-list-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
}

.no-users {
  padding: 40px 20px;
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.user-item:hover {
  background: #f8f9fa;
}

.user-avatar {
  position: relative;
  margin-right: 12px;
}

.user-avatar .avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
}

.online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #28a745;
  border: 2px solid white;
  border-radius: 50%;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 600;
  color: #495057;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-status {
  font-size: 0.8rem;
  color: #28a745;
  font-weight: 500;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .user-list-header {
    padding: 15px;
  }
  
  .user-list-header h3 {
    font-size: 1rem;
  }
  
  .user-item {
    padding: 10px 15px;
  }
  
  .user-avatar {
    margin-right: 10px;
  }
  
  .user-avatar .avatar {
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
  }
  
  .online-indicator {
    width: 10px;
    height: 10px;
    bottom: 1px;
    right: 1px;
  }
  
  .user-name {
    font-size: 0.9rem;
  }
  
  .user-status {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .user-list-header {
    padding: 12px;
  }
  
  .user-item {
    padding: 8px 12px;
  }
  
  .user-avatar {
    margin-right: 8px;
  }
  
  .user-avatar .avatar {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }
  
  .online-indicator {
    width: 8px;
    height: 8px;
  }
  
  .user-name {
    font-size: 0.85rem;
  }
  
  .user-status {
    font-size: 0.7rem;
  }
}
