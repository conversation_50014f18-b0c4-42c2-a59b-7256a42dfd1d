@echo off
echo Starting GlesChat Application...
echo.

echo Installing Python dependencies...
cd backend
pip install -r requirements.txt
echo.

echo Starting Backend Server...
start "Backend" cmd /k "python app.py"
cd ..

echo Waiting for backend to start...
timeout /t 5 /nobreak > nul

echo Starting Frontend Server...
start "Frontend" cmd /k "npm run dev"

echo.
echo Both servers are starting...
echo Backend: http://localhost:5000
echo Frontend: http://localhost:5173 (or next available port)
echo Admin Panel: http://localhost:5000/admin
echo.
echo Press any key to exit...
pause > nul
