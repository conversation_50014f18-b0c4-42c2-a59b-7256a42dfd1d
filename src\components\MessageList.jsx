import { formatTime } from '../utils/dateUtils'
import './MessageList.css'

const MessageList = ({ messages, currentUser }) => {
  if (!messages || messages.length === 0) {
    return (
      <div className="messages-empty">
        <p>还没有消息，开始聊天吧！</p>
      </div>
    )
  }

  return (
    <div className="messages-list">
      {messages.map((message, index) => {
        const isOwnMessage = message.user_id === currentUser?.id
        const showAvatar = index === 0 || messages[index - 1].user_id !== message.user_id
        const showTime = index === 0 || 
          new Date(message.timestamp).getTime() - new Date(messages[index - 1].timestamp).getTime() > 300000 // 5分钟

        return (
          <div key={message.id} className="message-wrapper">
            {showTime && (
              <div className="message-time-divider">
                <span>{formatTime(message.timestamp)}</span>
              </div>
            )}
            
            <div className={`message ${isOwnMessage ? 'own' : 'other'}`}>
              {!isOwnMessage && showAvatar && (
                <div className="message-avatar">
                  <div className="avatar">
                    {message.username?.charAt(0).toUpperCase()}
                  </div>
                </div>
              )}
              
              <div className="message-content">
                {!isOwnMessage && showAvatar && (
                  <div className="message-username">
                    {message.username}
                  </div>
                )}
                
                <div className="message-bubble">
                  <div className="message-text">
                    {message.content}
                  </div>
                  <div className="message-timestamp">
                    {new Date(message.timestamp).toLocaleTimeString('zh-CN', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default MessageList
