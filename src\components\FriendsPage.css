.friends-page {
  height: 100%;
  overflow-y: auto;
  background: #f8f9fa;
}

.friends-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px 20px;
}

.friends-loading {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6c757d;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin: 0 0 10px 0;
}

.page-header p {
  font-size: 1.1rem;
  color: #6c757d;
  margin: 0;
}

.tabs-container {
  margin-bottom: 30px;
}

.tabs {
  display: flex;
  background: white;
  border-radius: 15px;
  padding: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  margin: 0 auto;
}

.tab {
  flex: 1;
  padding: 12px 20px;
  border: none;
  background: transparent;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  color: #6c757d;
  transition: all 0.3s ease;
}

.tab:hover {
  color: #495057;
}

.tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.message {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  padding: 12px 16px;
  border-radius: 10px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  border-color: #f5c6cb;
}

.message button {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0;
  margin-left: 10px;
}

.content-area {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.section-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.add-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.add-form {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 25px;
}

.add-form input,
.add-form textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  margin-bottom: 15px;
  font-family: inherit;
}

.add-form input:focus,
.add-form textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-actions {
  display: flex;
  gap: 10px;
}

.form-actions button {
  flex: 1;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.form-actions button[type="submit"] {
  background: #667eea;
  color: white;
}

.form-actions button[type="button"] {
  background: #6c757d;
  color: white;
}

.form-actions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin: 0 0 10px 0;
  color: #495057;
}

.empty-state p {
  margin: 0;
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.friend-card,
.group-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.friend-card:hover,
.group-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.friend-avatar,
.group-avatar {
  position: relative;
  margin-bottom: 15px;
}

.avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.5rem;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.avatar.group {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  background: #28a745;
  border: 3px solid white;
  border-radius: 50%;
}

.friend-info,
.group-info {
  margin-bottom: 15px;
}

.friend-info h3,
.group-info h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 5px 0;
}

.friend-info p,
.group-info p {
  color: #6c757d;
  margin: 0;
  font-size: 0.9rem;
}

.friend-actions,
.group-actions {
  width: 100%;
}

.chat-btn,
.enter-btn {
  width: 100%;
  background: #28a745;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.chat-btn:hover,
.enter-btn:hover {
  background: #218838;
}

.requests-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.request-item {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  gap: 15px;
}

.request-avatar .avatar {
  width: 50px;
  height: 50px;
  font-size: 1.2rem;
}

.request-info {
  flex: 1;
}

.request-info h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 5px 0;
}

.request-info p {
  color: #6c757d;
  margin: 0;
  font-size: 0.9rem;
}

.request-actions {
  display: flex;
  gap: 10px;
}

.accept-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
}

.reject-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .friends-container {
    padding: 20px 15px;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
  
  .tabs {
    padding: 6px;
  }
  
  .tab {
    padding: 10px 16px;
    font-size: 0.9rem;
  }
  
  .content-area {
    padding: 20px;
  }
  
  .items-grid {
    grid-template-columns: 1fr;
  }
  
  .request-item {
    flex-direction: column;
    text-align: center;
  }
  
  .request-actions {
    width: 100%;
  }
  
  .accept-btn,
  .reject-btn {
    flex: 1;
  }
}

@media (max-width: 480px) {
  .friends-container {
    padding: 15px 10px;
  }
  
  .page-header h1 {
    font-size: 1.8rem;
  }
  
  .content-area {
    padding: 15px;
  }
  
  .section-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .add-btn {
    width: 100%;
  }
}
