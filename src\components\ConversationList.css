.conversation-list {
  height: 100%;
  overflow-y: auto;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
  color: #6c757d;
}

.empty-state p {
  margin: 5px 0;
  font-size: 0.9rem;
}

.conversation-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.conversation-item:hover {
  background-color: #f8f9fa;
}

.conversation-item.active {
  background-color: #e3f2fd;
  border-right: 3px solid #667eea;
}

.conversation-avatar {
  position: relative;
  margin-right: 12px;
  flex-shrink: 0;
}

.conversation-avatar .avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
  color: white;
}

.conversation-avatar .avatar.private {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.conversation-avatar .avatar.group {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #28a745;
  border: 2px solid white;
  border-radius: 50%;
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.conversation-title {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.last-message-time {
  font-size: 0.75rem;
  color: #6c757d;
  flex-shrink: 0;
  margin-left: 8px;
}

.conversation-preview {
  display: flex;
  align-items: center;
}

.last-message {
  font-size: 0.85rem;
  color: #6c757d;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sender-name {
  font-weight: 500;
  color: #495057;
  margin-right: 4px;
}

.no-message {
  font-style: italic;
  color: #adb5bd;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .conversation-item {
    padding: 10px 12px;
  }
  
  .conversation-avatar {
    margin-right: 10px;
  }
  
  .conversation-avatar .avatar {
    width: 42px;
    height: 42px;
    font-size: 1rem;
  }
  
  .conversation-title {
    font-size: 0.95rem;
  }
  
  .last-message {
    font-size: 0.8rem;
  }
  
  .last-message-time {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .conversation-item {
    padding: 8px 10px;
  }
  
  .conversation-avatar .avatar {
    width: 38px;
    height: 38px;
    font-size: 0.9rem;
  }
  
  .online-indicator {
    width: 10px;
    height: 10px;
  }
}
