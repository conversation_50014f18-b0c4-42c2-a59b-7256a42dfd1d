import './UserList.css'

const UserList = ({ users }) => {
  return (
    <div className="user-list">
      <div className="user-list-header">
        <h3>在线用户 ({users.length})</h3>
      </div>
      
      <div className="user-list-content">
        {users.length === 0 ? (
          <div className="no-users">
            <p>暂无在线用户</p>
          </div>
        ) : (
          users.map(user => (
            <div key={user.id} className="user-item">
              <div className="user-avatar">
                <div className="avatar">
                  {user.username?.charAt(0).toUpperCase()}
                </div>
                <div className="online-indicator"></div>
              </div>
              
              <div className="user-info">
                <div className="user-name">{user.username}</div>
                <div className="user-status">在线</div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}

export default UserList
