import { useState } from 'react'
import axios from 'axios'
import './GroupsList.css'

const GroupsList = ({ groups, onRefresh, onSelectGroup }) => {
  const [showCreateGroup, setShowCreateGroup] = useState(false)
  const [groupName, setGroupName] = useState('')
  const [groupDescription, setGroupDescription] = useState('')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')

  const handleCreateGroup = async (e) => {
    e.preventDefault()
    if (!groupName.trim()) return

    setLoading(true)
    setMessage('')

    try {
      const response = await axios.post('/api/groups', {
        name: groupName.trim(),
        description: groupDescription.trim()
      })
      
      setMessage('群组创建成功')
      setGroupName('')
      setGroupDescription('')
      setShowCreateGroup(false)
      onRefresh()
    } catch (error) {
      setMessage(error.response?.data?.message || '创建群组失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="groups-list">
      <div className="groups-header">
        <h3>群组列表 ({groups.length})</h3>
        <button 
          className="create-group-btn"
          onClick={() => setShowCreateGroup(!showCreateGroup)}
        >
          ➕
        </button>
      </div>

      {message && (
        <div className={`message ${message.includes('失败') ? 'error' : 'success'}`}>
          {message}
        </div>
      )}

      {showCreateGroup && (
        <div className="create-group-form">
          <form onSubmit={handleCreateGroup}>
            <input
              type="text"
              placeholder="群组名称"
              value={groupName}
              onChange={(e) => setGroupName(e.target.value)}
              disabled={loading}
              required
            />
            <textarea
              placeholder="群组描述（可选）"
              value={groupDescription}
              onChange={(e) => setGroupDescription(e.target.value)}
              disabled={loading}
              rows="3"
            />
            <div className="form-actions">
              <button type="submit" disabled={loading || !groupName.trim()}>
                {loading ? '创建中...' : '创建群组'}
              </button>
              <button 
                type="button" 
                onClick={() => setShowCreateGroup(false)}
                disabled={loading}
              >
                取消
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="groups-content">
        {groups.length === 0 ? (
          <div className="empty-state">
            <p>暂无群组</p>
            <p>点击 ➕ 创建群组</p>
          </div>
        ) : (
          groups.map(group => (
            <div 
              key={group.id} 
              className="group-item"
              onClick={() => onSelectGroup(group)}
            >
              <div className="group-avatar">
                <div className="avatar group">
                  {group.name.charAt(0).toUpperCase()}
                </div>
              </div>
              
              <div className="group-info">
                <h4 className="group-name">{group.name}</h4>
                <p className="group-description">
                  {group.description || '暂无描述'}
                </p>
                <p className="group-meta">
                  创建者: {group.creator.username}
                </p>
              </div>
              
              <div className="group-actions">
                <button 
                  className="enter-btn"
                  onClick={(e) => {
                    e.stopPropagation()
                    onSelectGroup(group)
                  }}
                  title="进入群组"
                >
                  →
                </button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}

export default GroupsList
