import { formatTime } from '../utils/dateUtils'
import './ConversationList.css'

const ConversationList = ({ conversations, currentConversation, onSelectConversation }) => {
  const getConversationTitle = (conversation) => {
    if (conversation.type === 'private') {
      return conversation.other_user?.username || '私聊'
    } else if (conversation.type === 'group') {
      return conversation.group?.name || '群聊'
    }
    return '未知会话'
  }

  const getConversationAvatar = (conversation) => {
    if (conversation.type === 'private') {
      return conversation.other_user?.username?.charAt(0).toUpperCase() || 'U'
    } else if (conversation.type === 'group') {
      return conversation.group?.name?.charAt(0).toUpperCase() || 'G'
    }
    return 'C'
  }

  const getLastMessagePreview = (conversation) => {
    if (conversation.last_message) {
      const content = conversation.last_message.content
      return content.length > 30 ? content.substring(0, 30) + '...' : content
    }
    return '暂无消息'
  }

  if (!conversations || conversations.length === 0) {
    return (
      <div className="conversation-list">
        <div className="empty-state">
          <p>暂无会话</p>
          <p>添加好友或创建群组开始聊天</p>
        </div>
      </div>
    )
  }

  return (
    <div className="conversation-list">
      {conversations.map(conversation => (
        <div 
          key={conversation.id}
          className={`conversation-item ${currentConversation?.id === conversation.id ? 'active' : ''}`}
          onClick={() => onSelectConversation(conversation)}
        >
          <div className="conversation-avatar">
            <div className={`avatar ${conversation.type}`}>
              {getConversationAvatar(conversation)}
            </div>
            {conversation.type === 'private' && conversation.other_user?.is_online && (
              <div className="online-indicator"></div>
            )}
          </div>
          
          <div className="conversation-info">
            <div className="conversation-header">
              <h3 className="conversation-title">
                {getConversationTitle(conversation)}
              </h3>
              {conversation.last_message && (
                <span className="last-message-time">
                  {formatTime(conversation.last_message.timestamp)}
                </span>
              )}
            </div>
            
            <div className="conversation-preview">
              <span className="last-message">
                {conversation.last_message && (
                  <>
                    {conversation.type === 'group' && (
                      <span className="sender-name">
                        {conversation.last_message.sender.username}: 
                      </span>
                    )}
                    {getLastMessagePreview(conversation)}
                  </>
                )}
                {!conversation.last_message && (
                  <span className="no-message">暂无消息</span>
                )}
              </span>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export default ConversationList
