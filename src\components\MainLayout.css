.main-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.main-header {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 0 20px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
}

.app-title {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.main-nav {
  display: flex;
  gap: 8px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  background: transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #6c757d;
  font-size: 1rem;
  font-weight: 500;
}

.nav-item:hover {
  background: #f8f9fa;
  color: #495057;
}

.nav-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.nav-icon {
  font-size: 1.2rem;
}

.nav-label {
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 20px;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
}

.username {
  font-weight: 600;
  color: #495057;
}

.admin-btn {
  background: #28a745;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.admin-btn:hover {
  background: #218838;
  transform: scale(1.1);
}

.logout-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: #c82333;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.main-content {
  flex: 1;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .main-header {
    padding: 0 15px;
    height: 60px;
  }
  
  .app-title {
    font-size: 1.5rem;
  }
  
  .main-nav {
    gap: 4px;
  }
  
  .nav-item {
    padding: 8px 12px;
    font-size: 0.9rem;
  }
  
  .nav-label {
    display: none;
  }
  
  .nav-icon {
    font-size: 1.4rem;
  }
  
  .username {
    display: none;
  }
  
  .admin-btn {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }
  
  .logout-btn {
    padding: 8px 16px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .main-header {
    padding: 0 10px;
  }
  
  .app-title {
    font-size: 1.3rem;
  }
  
  .nav-item {
    padding: 6px 10px;
  }
  
  .user-info {
    padding: 6px 8px;
  }
  
  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }
  
  .admin-btn {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }
  
  .logout-btn {
    padding: 6px 12px;
  }
}
