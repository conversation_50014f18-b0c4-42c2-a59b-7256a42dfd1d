# GlesChat - 即时通讯应用

一个基于 React + Flask 的现代化即时通讯应用，支持PC端和移动端。

## 功能特性

- 🔐 用户注册/登录系统
- 💬 实时消息传输
- 👥 在线用户列表
- 📱 响应式设计，完美适配移动端
- 🎨 现代化UI设计
- ⚡ 基于WebSocket的实时通信

## 技术栈

### 前端
- React 19
- Vite
- Socket.IO Client
- React Router
- Axios

### 后端
- Flask
- Flask-SocketIO
- Flask-SQLAlchemy
- Flask-JWT-Extended
- SQLite

## 项目结构

```
gleschat/
├── src/                    # 前端源码
│   ├── components/         # React组件
│   │   ├── HomePage.jsx    # 登录/注册页面
│   │   ├── ChatPage.jsx    # 聊天主页面
│   │   ├── MessageList.jsx # 消息列表
│   │   ├── MessageInput.jsx# 消息输入框
│   │   └── UserList.jsx    # 用户列表
│   ├── contexts/           # React上下文
│   │   └── AuthContext.jsx # 认证上下文
│   ├── hooks/              # 自定义Hook
│   │   └── useSocket.js    # Socket连接Hook
│   └── utils/              # 工具函数
│       └── dateUtils.js    # 日期处理
├── backend/                # 后端源码
│   ├── app.py             # Flask应用主文件
│   └── requirements.txt   # Python依赖
└── public/                # 静态资源
```

## 安装和运行

### 前端设置

1. 安装依赖：
```bash
npm install
```

2. 启动开发服务器：
```bash
npm run dev
```

前端将在 http://localhost:5173 运行

### 后端设置

1. 进入后端目录：
```bash
cd backend
```

2. 创建虚拟环境（推荐）：
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. 安装Python依赖：
```bash
pip install -r requirements.txt
```

4. 启动Flask服务器：
```bash
python app.py
```

后端将在 http://localhost:5000 运行

## 使用说明

1. 打开浏览器访问 http://localhost:5173
2. 注册新账号或使用现有账号登录
3. 进入聊天界面开始聊天
4. 可以查看在线用户列表
5. 支持多设备同时在线

## 移动端适配

应用采用响应式设计，在移动设备上会自动适配：
- 优化的触摸界面
- 适合手机屏幕的布局
- 滑动手势支持
- 移动端键盘优化

## 开发说明

### 添加新功能
- 前端组件放在 `src/components/` 目录
- 样式文件与组件同名，使用 `.css` 扩展名
- 全局状态使用 React Context
- API调用统一使用 Axios

### 后端API
- 认证相关：`/api/login`, `/api/register`, `/api/logout`
- 消息相关：`/api/messages`
- 用户相关：`/api/users`
- WebSocket事件：`connect`, `disconnect`, `send_message`

## 部署

### 前端部署
```bash
npm run build
```
构建文件将生成在 `dist/` 目录

### 后端部署
建议使用 Gunicorn + Nginx 部署Flask应用

## 许可证

MIT License
