import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import axios from 'axios'
import './FriendsPage.css'

const FriendsPage = () => {
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState('friends') // friends, requests, groups
  const [friends, setFriends] = useState([])
  const [friendRequests, setFriendRequests] = useState([])
  const [groups, setGroups] = useState([])
  const [loading, setLoading] = useState(true)
  
  // 添加好友相关状态
  const [showAddFriend, setShowAddFriend] = useState(false)
  const [friendUsername, setFriendUsername] = useState('')
  const [addFriendLoading, setAddFriendLoading] = useState(false)
  const [message, setMessage] = useState('')
  
  // 创建群组相关状态
  const [showCreateGroup, setShowCreateGroup] = useState(false)
  const [groupName, setGroupName] = useState('')
  const [groupDescription, setGroupDescription] = useState('')
  const [createGroupLoading, setCreateGroupLoading] = useState(false)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    setLoading(true)
    try {
      const [friendsRes, requestsRes, groupsRes] = await Promise.all([
        fetch('/api/friends', {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        }),
        fetch('/api/friends/requests', {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        }),
        fetch('/api/groups', {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        })
      ])

      if (friendsRes.ok) setFriends(await friendsRes.json())
      if (requestsRes.ok) setFriendRequests(await requestsRes.json())
      if (groupsRes.ok) setGroups(await groupsRes.json())
    } catch (error) {
      console.error('加载数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddFriend = async (e) => {
    e.preventDefault()
    if (!friendUsername.trim()) return

    setAddFriendLoading(true)
    setMessage('')

    try {
      await axios.post('/api/friends/add', {
        username: friendUsername.trim()
      })
      
      setMessage('好友请求已发送')
      setFriendUsername('')
      setShowAddFriend(false)
      loadData()
    } catch (error) {
      setMessage(error.response?.data?.message || '添加好友失败')
    } finally {
      setAddFriendLoading(false)
    }
  }

  const handleAcceptRequest = async (requestId) => {
    try {
      await axios.post(`/api/friends/accept/${requestId}`)
      loadData()
    } catch (error) {
      console.error('接受好友请求失败:', error)
    }
  }

  const handleRejectRequest = async (requestId) => {
    try {
      await axios.post(`/api/friends/reject/${requestId}`)
      loadData()
    } catch (error) {
      console.error('拒绝好友请求失败:', error)
    }
  }

  const handleCreateGroup = async (e) => {
    e.preventDefault()
    if (!groupName.trim()) return

    setCreateGroupLoading(true)

    try {
      await axios.post('/api/groups', {
        name: groupName.trim(),
        description: groupDescription.trim()
      })
      
      setGroupName('')
      setGroupDescription('')
      setShowCreateGroup(false)
      loadData()
    } catch (error) {
      console.error('创建群组失败:', error)
    } finally {
      setCreateGroupLoading(false)
    }
  }

  const startPrivateChat = async (friend) => {
    try {
      const response = await axios.post('/api/conversations/private', {
        friend_id: friend.id
      })
      navigate(`/chat/${response.data.id}`)
    } catch (error) {
      console.error('创建私聊失败:', error)
    }
  }

  const enterGroup = (group) => {
    // 这里需要找到群组对应的会话ID
    // 暂时直接跳转到聊天页面
    navigate('/chat')
  }

  if (loading) {
    return (
      <div className="friends-loading">
        <div className="loading-spinner"></div>
        <p>加载中...</p>
      </div>
    )
  }

  return (
    <div className="friends-page">
      <div className="friends-container">
        {/* 页面标题 */}
        <div className="page-header">
          <h1>好友与群组</h1>
          <p>管理你的好友关系和群组</p>
        </div>

        {/* 标签切换 */}
        <div className="tabs-container">
          <div className="tabs">
            <button 
              className={`tab ${activeTab === 'friends' ? 'active' : ''}`}
              onClick={() => setActiveTab('friends')}
            >
              好友 ({friends.length})
            </button>
            <button 
              className={`tab ${activeTab === 'requests' ? 'active' : ''}`}
              onClick={() => setActiveTab('requests')}
            >
              请求 ({friendRequests.length})
            </button>
            <button 
              className={`tab ${activeTab === 'groups' ? 'active' : ''}`}
              onClick={() => setActiveTab('groups')}
            >
              群组 ({groups.length})
            </button>
          </div>
        </div>

        {/* 消息提示 */}
        {message && (
          <div className={`message ${message.includes('失败') ? 'error' : 'success'}`}>
            {message}
            <button onClick={() => setMessage('')}>×</button>
          </div>
        )}

        {/* 内容区域 */}
        <div className="content-area">
          {activeTab === 'friends' && (
            <div className="friends-content">
              <div className="section-header">
                <h2>我的好友</h2>
                <button 
                  className="add-btn"
                  onClick={() => setShowAddFriend(!showAddFriend)}
                >
                  ➕ 添加好友
                </button>
              </div>

              {showAddFriend && (
                <div className="add-form">
                  <form onSubmit={handleAddFriend}>
                    <input
                      type="text"
                      placeholder="输入用户名"
                      value={friendUsername}
                      onChange={(e) => setFriendUsername(e.target.value)}
                      disabled={addFriendLoading}
                    />
                    <div className="form-actions">
                      <button type="submit" disabled={addFriendLoading || !friendUsername.trim()}>
                        {addFriendLoading ? '发送中...' : '发送请求'}
                      </button>
                      <button 
                        type="button" 
                        onClick={() => setShowAddFriend(false)}
                        disabled={addFriendLoading}
                      >
                        取消
                      </button>
                    </div>
                  </form>
                </div>
              )}

              {friends.length === 0 ? (
                <div className="empty-state">
                  <div className="empty-icon">👥</div>
                  <h3>还没有好友</h3>
                  <p>添加好友开始聊天</p>
                </div>
              ) : (
                <div className="items-grid">
                  {friends.map(friend => (
                    <div key={friend.id} className="friend-card">
                      <div className="friend-avatar">
                        <div className="avatar">
                          {friend.username.charAt(0).toUpperCase()}
                        </div>
                        {friend.is_online && <div className="online-indicator"></div>}
                      </div>
                      
                      <div className="friend-info">
                        <h3>{friend.username}</h3>
                        <p>{friend.is_online ? '在线' : '离线'}</p>
                      </div>
                      
                      <div className="friend-actions">
                        <button 
                          className="chat-btn"
                          onClick={() => startPrivateChat(friend)}
                        >
                          💬 聊天
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'requests' && (
            <div className="requests-content">
              <h2>好友请求</h2>
              
              {friendRequests.length === 0 ? (
                <div className="empty-state">
                  <div className="empty-icon">📬</div>
                  <h3>没有新的好友请求</h3>
                  <p>当有人向你发送好友请求时，会显示在这里</p>
                </div>
              ) : (
                <div className="requests-list">
                  {friendRequests.map(request => (
                    <div key={request.id} className="request-item">
                      <div className="request-avatar">
                        <div className="avatar">
                          {request.user.username.charAt(0).toUpperCase()}
                        </div>
                      </div>
                      
                      <div className="request-info">
                        <h3>{request.user.username}</h3>
                        <p>想要添加你为好友</p>
                      </div>
                      
                      <div className="request-actions">
                        <button 
                          className="accept-btn"
                          onClick={() => handleAcceptRequest(request.id)}
                        >
                          接受
                        </button>
                        <button 
                          className="reject-btn"
                          onClick={() => handleRejectRequest(request.id)}
                        >
                          拒绝
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'groups' && (
            <div className="groups-content">
              <div className="section-header">
                <h2>我的群组</h2>
                <button 
                  className="add-btn"
                  onClick={() => setShowCreateGroup(!showCreateGroup)}
                >
                  ➕ 创建群组
                </button>
              </div>

              {showCreateGroup && (
                <div className="add-form">
                  <form onSubmit={handleCreateGroup}>
                    <input
                      type="text"
                      placeholder="群组名称"
                      value={groupName}
                      onChange={(e) => setGroupName(e.target.value)}
                      disabled={createGroupLoading}
                      required
                    />
                    <textarea
                      placeholder="群组描述（可选）"
                      value={groupDescription}
                      onChange={(e) => setGroupDescription(e.target.value)}
                      disabled={createGroupLoading}
                      rows="3"
                    />
                    <div className="form-actions">
                      <button type="submit" disabled={createGroupLoading || !groupName.trim()}>
                        {createGroupLoading ? '创建中...' : '创建群组'}
                      </button>
                      <button 
                        type="button" 
                        onClick={() => setShowCreateGroup(false)}
                        disabled={createGroupLoading}
                      >
                        取消
                      </button>
                    </div>
                  </form>
                </div>
              )}

              {groups.length === 0 ? (
                <div className="empty-state">
                  <div className="empty-icon">🏢</div>
                  <h3>还没有群组</h3>
                  <p>创建群组与多人聊天</p>
                </div>
              ) : (
                <div className="items-grid">
                  {groups.map(group => (
                    <div key={group.id} className="group-card">
                      <div className="group-avatar">
                        <div className="avatar group">
                          {group.name.charAt(0).toUpperCase()}
                        </div>
                      </div>
                      
                      <div className="group-info">
                        <h3>{group.name}</h3>
                        <p>{group.description || '暂无描述'}</p>
                      </div>
                      
                      <div className="group-actions">
                        <button 
                          className="enter-btn"
                          onClick={() => enterGroup(group)}
                        >
                          进入群组
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default FriendsPage
