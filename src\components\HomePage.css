.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.home-container {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 100%;
  max-width: 400px;
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.home-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  padding: 40px 20px;
}

.home-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.home-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

.auth-container {
  padding: 30px;
}

.auth-tabs {
  display: flex;
  margin-bottom: 30px;
  border-radius: 10px;
  background: #f8f9fa;
  padding: 4px;
}

.tab {
  flex: 1;
  padding: 12px;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  color: #6c757d;
}

.tab.active {
  background: #667eea;
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  position: relative;
}

.form-group input {
  width: 100%;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.error-message {
  background: #fee;
  color: #c33;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #fcc;
  font-size: 14px;
  text-align: center;
}

.auth-switch {
  text-align: center;
  margin-top: 25px;
  padding-top: 25px;
  border-top: 1px solid #e9ecef;
}

.auth-switch p {
  margin: 0;
  color: #6c757d;
}

.switch-btn {
  background: none;
  border: none;
  color: #667eea;
  font-weight: 600;
  cursor: pointer;
  margin-left: 5px;
  text-decoration: underline;
}

.switch-btn:hover {
  color: #764ba2;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .home-page {
    padding: 10px;
  }
  
  .home-container {
    max-width: 100%;
    border-radius: 15px;
  }
  
  .home-header {
    padding: 30px 20px;
  }
  
  .home-header h1 {
    font-size: 2rem;
  }
  
  .auth-container {
    padding: 20px;
  }
  
  .form-group input {
    padding: 12px;
    font-size: 16px; /* 防止iOS缩放 */
  }
  
  .submit-btn {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .home-header h1 {
    font-size: 1.8rem;
  }
  
  .home-header p {
    font-size: 1rem;
  }
  
  .auth-container {
    padding: 15px;
  }
}
