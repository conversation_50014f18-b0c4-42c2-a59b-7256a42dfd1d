.groups-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.groups-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.groups-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #495057;
}

.create-group-btn {
  background: #28a745;
  color: white;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.create-group-btn:hover {
  background: #218838;
  transform: scale(1.1);
}

.message {
  padding: 8px 16px;
  margin: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
}

.message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.create-group-form {
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.create-group-form input,
.create-group-form textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  margin-bottom: 10px;
  font-family: inherit;
  resize: vertical;
}

.create-group-form input:focus,
.create-group-form textarea:focus {
  outline: none;
  border-color: #28a745;
  box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
}

.form-actions {
  display: flex;
  gap: 8px;
}

.form-actions button {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.form-actions button[type="submit"] {
  background: #28a745;
  color: white;
}

.form-actions button[type="submit"]:hover:not(:disabled) {
  background: #218838;
}

.form-actions button[type="button"] {
  background: #6c757d;
  color: white;
}

.form-actions button[type="button"]:hover:not(:disabled) {
  background: #5a6268;
}

.form-actions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.groups-content {
  flex: 1;
  overflow-y: auto;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
  color: #6c757d;
}

.empty-state p {
  margin: 5px 0;
  font-size: 0.9rem;
}

.group-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.group-item:hover {
  background-color: #f8f9fa;
}

.group-avatar {
  margin-right: 12px;
  flex-shrink: 0;
}

.group-avatar .avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
  color: white;
}

.group-avatar .avatar.group {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.group-info {
  flex: 1;
  min-width: 0;
}

.group-name {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.group-description {
  font-size: 0.85rem;
  color: #6c757d;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.group-meta {
  font-size: 0.75rem;
  color: #adb5bd;
  margin: 0;
}

.group-actions {
  display: flex;
  gap: 8px;
}

.enter-btn {
  background: #667eea;
  color: white;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.enter-btn:hover {
  background: #5a6fd8;
  transform: scale(1.1);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .groups-header {
    padding: 12px;
  }
  
  .create-group-form {
    padding: 12px;
  }
  
  .group-item {
    padding: 10px 12px;
  }
  
  .group-avatar {
    margin-right: 10px;
  }
  
  .group-avatar .avatar {
    width: 42px;
    height: 42px;
    font-size: 1rem;
  }
  
  .enter-btn {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .groups-header h3 {
    font-size: 1rem;
  }
  
  .create-group-btn {
    width: 28px;
    height: 28px;
    font-size: 1rem;
  }
  
  .group-item {
    padding: 8px 10px;
  }
  
  .group-name {
    font-size: 0.95rem;
  }
  
  .group-description {
    font-size: 0.8rem;
  }
  
  .group-meta {
    font-size: 0.7rem;
  }
}
