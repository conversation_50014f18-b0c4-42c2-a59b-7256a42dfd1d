.messages-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.messages-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6c757d;
  font-style: italic;
}

.message-wrapper {
  width: 100%;
}

.message-time-divider {
  text-align: center;
  margin: 20px 0 15px 0;
}

.message-time-divider span {
  background: #e9ecef;
  color: #6c757d;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.message {
  display: flex;
  margin-bottom: 8px;
  max-width: 100%;
}

.message.own {
  justify-content: flex-end;
}

.message.other {
  justify-content: flex-start;
}

.message-avatar {
  margin-right: 10px;
  margin-top: 5px;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.message-content {
  max-width: 70%;
  min-width: 0;
}

.message.own .message-content {
  max-width: 80%;
}

.message-username {
  font-size: 0.8rem;
  color: #667eea;
  font-weight: 600;
  margin-bottom: 4px;
  margin-left: 12px;
}

.message-bubble {
  position: relative;
  padding: 12px 16px 8px 16px;
  border-radius: 18px;
  word-wrap: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message.own .message-bubble {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom-right-radius: 6px;
}

.message.other .message-bubble {
  background: white;
  color: #333;
  border: 1px solid #e9ecef;
  border-bottom-left-radius: 6px;
}

.message-text {
  line-height: 1.4;
  margin-bottom: 4px;
  white-space: pre-wrap;
}

.message-timestamp {
  font-size: 0.7rem;
  opacity: 0.7;
  text-align: right;
  margin-top: 4px;
}

.message.other .message-timestamp {
  color: #6c757d;
}

.message.own .message-timestamp {
  color: rgba(255, 255, 255, 0.8);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .message-content {
    max-width: 85%;
  }
  
  .message.own .message-content {
    max-width: 90%;
  }
  
  .avatar {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }
  
  .message-avatar {
    margin-right: 8px;
  }
  
  .message-bubble {
    padding: 10px 14px 6px 14px;
  }
  
  .message-username {
    margin-left: 10px;
  }
}

@media (max-width: 480px) {
  .message-content {
    max-width: 90%;
  }
  
  .message.own .message-content {
    max-width: 95%;
  }
  
  .avatar {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }
  
  .message-bubble {
    padding: 8px 12px 5px 12px;
    font-size: 0.9rem;
  }
  
  .message-username {
    font-size: 0.75rem;
    margin-left: 8px;
  }
  
  .message-timestamp {
    font-size: 0.65rem;
  }
}
