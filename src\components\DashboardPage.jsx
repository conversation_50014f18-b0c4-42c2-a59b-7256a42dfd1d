import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import './DashboardPage.css'

const DashboardPage = () => {
  const { user } = useAuth()
  const navigate = useNavigate()
  const [stats, setStats] = useState({
    friendsCount: 0,
    groupsCount: 0,
    unreadMessages: 0
  })
  const [recentConversations, setRecentConversations] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      // 加载统计数据
      const [friendsRes, groupsRes, conversationsRes] = await Promise.all([
        fetch('/api/friends', {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        }),
        fetch('/api/groups', {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        }),
        fetch('/api/conversations', {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        })
      ])

      if (friendsRes.ok && groupsRes.ok && conversationsRes.ok) {
        const friends = await friendsRes.json()
        const groups = await groupsRes.json()
        const conversations = await conversationsRes.json()

        setStats({
          friendsCount: friends.length,
          groupsCount: groups.length,
          unreadMessages: 0 // 暂时设为0，后续可以实现未读消息统计
        })

        // 取最近的5个会话
        setRecentConversations(conversations.slice(0, 5))
      }
    } catch (error) {
      console.error('加载仪表板数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getConversationTitle = (conversation) => {
    if (conversation.type === 'private') {
      return conversation.other_user?.username || '私聊'
    } else if (conversation.type === 'group') {
      return conversation.group?.name || '群聊'
    }
    return '未知会话'
  }

  const formatTime = (timestamp) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    
    if (diff < 60000) return '刚刚'
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    }
    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
  }

  if (loading) {
    return (
      <div className="dashboard-loading">
        <div className="loading-spinner"></div>
        <p>加载中...</p>
      </div>
    )
  }

  return (
    <div className="dashboard-page">
      <div className="dashboard-container">
        {/* 欢迎区域 */}
        <section className="welcome-section">
          <h1>欢迎回来，{user?.username}！</h1>
          <p>开始与朋友聊天，或者创建新的群组</p>
        </section>

        {/* 统计卡片 */}
        <section className="stats-section">
          <div className="stats-grid">
            <div className="stat-card" onClick={() => navigate('/friends')}>
              <div className="stat-icon">👥</div>
              <div className="stat-content">
                <h3>{stats.friendsCount}</h3>
                <p>好友</p>
              </div>
            </div>
            
            <div className="stat-card" onClick={() => navigate('/friends')}>
              <div className="stat-icon">🏢</div>
              <div className="stat-content">
                <h3>{stats.groupsCount}</h3>
                <p>群组</p>
              </div>
            </div>
            
            <div className="stat-card">
              <div className="stat-icon">💬</div>
              <div className="stat-content">
                <h3>{stats.unreadMessages}</h3>
                <p>未读消息</p>
              </div>
            </div>
          </div>
        </section>

        {/* 最近会话 */}
        <section className="recent-section">
          <div className="section-header">
            <h2>最近会话</h2>
            <button 
              className="view-all-btn"
              onClick={() => navigate('/chat')}
            >
              查看全部
            </button>
          </div>
          
          {recentConversations.length === 0 ? (
            <div className="empty-state">
              <div className="empty-icon">💬</div>
              <h3>还没有会话</h3>
              <p>添加好友或创建群组开始聊天</p>
              <div className="empty-actions">
                <button 
                  className="action-btn primary"
                  onClick={() => navigate('/friends')}
                >
                  添加好友
                </button>
              </div>
            </div>
          ) : (
            <div className="conversations-list">
              {recentConversations.map(conversation => (
                <div 
                  key={conversation.id}
                  className="conversation-item"
                  onClick={() => navigate(`/chat/${conversation.id}`)}
                >
                  <div className="conversation-avatar">
                    <div className={`avatar ${conversation.type}`}>
                      {conversation.type === 'private' 
                        ? conversation.other_user?.username?.charAt(0).toUpperCase()
                        : conversation.group?.name?.charAt(0).toUpperCase()
                      }
                    </div>
                  </div>
                  
                  <div className="conversation-info">
                    <h4>{getConversationTitle(conversation)}</h4>
                    <p>
                      {conversation.last_message 
                        ? conversation.last_message.content.substring(0, 50) + '...'
                        : '暂无消息'
                      }
                    </p>
                  </div>
                  
                  <div className="conversation-time">
                    {conversation.last_message && formatTime(conversation.last_message.timestamp)}
                  </div>
                </div>
              ))}
            </div>
          )}
        </section>

        {/* 快速操作 */}
        <section className="quick-actions">
          <h2>快速操作</h2>
          <div className="actions-grid">
            <button 
              className="action-card"
              onClick={() => navigate('/friends')}
            >
              <div className="action-icon">➕</div>
              <div className="action-content">
                <h3>添加好友</h3>
                <p>通过用户名搜索并添加好友</p>
              </div>
            </button>
            
            <button 
              className="action-card"
              onClick={() => navigate('/friends')}
            >
              <div className="action-icon">🏢</div>
              <div className="action-content">
                <h3>创建群组</h3>
                <p>创建新的群组开始群聊</p>
              </div>
            </button>
          </div>
        </section>
      </div>
    </div>
  )
}

export default DashboardPage
