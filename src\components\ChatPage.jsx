import { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import './ChatPage.css'

const ChatPage = () => {
  const { conversationId } = useParams()
  const { user } = useAuth()
  const [conversations, setConversations] = useState([])
  const [currentConversation, setCurrentConversation] = useState(null)
  const [messages, setMessages] = useState([])
  const [newMessage, setNewMessage] = useState('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadConversations()
    if (conversationId) {
      loadConversation(conversationId)
    }
  }, [conversationId])

  const loadConversations = async () => {
    try {
      const response = await fetch('/api/conversations', {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
      })
      if (response.ok) {
        const data = await response.json()
        setConversations(data)
      }
    } catch (error) {
      console.error('加载会话列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadConversation = async (id) => {
    try {
      const response = await fetch(`/api/conversations/${id}/messages`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
      })
      if (response.ok) {
        const data = await response.json()
        setMessages(data)

        // 设置当前会话
        const conversation = conversations.find(c => c.id === parseInt(id))
        setCurrentConversation(conversation)
      }
    } catch (error) {
      console.error('加载消息失败:', error)
    }
  }

  const sendMessage = async (e) => {
    e.preventDefault()
    if (!newMessage.trim() || !currentConversation) return

    try {
      const response = await fetch(`/api/conversations/${currentConversation.id}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ content: newMessage.trim() })
      })

      if (response.ok) {
        const message = await response.json()
        setMessages(prev => [...prev, message])
        setNewMessage('')
      }
    } catch (error) {
      console.error('发送消息失败:', error)
    }
  }

  const getConversationTitle = (conversation) => {
    if (!conversation) return ''
    if (conversation.type === 'private') {
      return conversation.other_user?.username || '私聊'
    } else if (conversation.type === 'group') {
      return conversation.group?.name || '群聊'
    }
    return '未知会话'
  }

  const formatTime = (timestamp) => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  }

  if (loading) {
    return (
      <div className="chat-loading">
        <div className="loading-spinner"></div>
        <p>加载中...</p>
      </div>
    )
  }

  return (
    <div className="chat-page">
      <div className="chat-container">
        {/* 左侧会话列表 */}
        <div className="conversations-sidebar">
          <div className="sidebar-header">
            <h2>会话</h2>
          </div>

          <div className="conversations-list">
            {conversations.length === 0 ? (
              <div className="empty-conversations">
                <p>还没有会话</p>
                <p>去好友页面开始聊天</p>
              </div>
            ) : (
              conversations.map(conversation => (
                <div
                  key={conversation.id}
                  className={`conversation-item ${currentConversation?.id === conversation.id ? 'active' : ''}`}
                  onClick={() => {
                    setCurrentConversation(conversation)
                    loadConversation(conversation.id)
                  }}
                >
                  <div className="conversation-avatar">
                    <div className={`avatar ${conversation.type}`}>
                      {conversation.type === 'private'
                        ? conversation.other_user?.username?.charAt(0).toUpperCase()
                        : conversation.group?.name?.charAt(0).toUpperCase()
                      }
                    </div>
                  </div>

                  <div className="conversation-info">
                    <h4>{getConversationTitle(conversation)}</h4>
                    <p>
                      {conversation.last_message
                        ? conversation.last_message.content.substring(0, 30) + '...'
                        : '暂无消息'
                      }
                    </p>
                  </div>

                  <div className="conversation-time">
                    {conversation.last_message && formatTime(conversation.last_message.timestamp)}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* 右侧聊天区域 */}
        <div className="chat-area">
          {currentConversation ? (
            <>
              {/* 聊天头部 */}
              <div className="chat-header">
                <div className="chat-title">
                  <h3>{getConversationTitle(currentConversation)}</h3>
                  <p>
                    {currentConversation.type === 'private'
                      ? (currentConversation.other_user?.is_online ? '在线' : '离线')
                      : `群组 · ${currentConversation.group?.members_count || 0} 人`
                    }
                  </p>
                </div>
              </div>

              {/* 消息列表 */}
              <div className="messages-container">
                {messages.length === 0 ? (
                  <div className="empty-messages">
                    <p>还没有消息，开始聊天吧！</p>
                  </div>
                ) : (
                  <div className="messages-list">
                    {messages.map(message => (
                      <div
                        key={message.id}
                        className={`message ${message.sender_id === user.id ? 'own' : 'other'}`}
                      >
                        <div className="message-content">
                          <div className="message-text">{message.content}</div>
                          <div className="message-time">{formatTime(message.timestamp)}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* 消息输入 */}
              <div className="message-input-container">
                <form onSubmit={sendMessage} className="message-form">
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="输入消息..."
                    className="message-input"
                  />
                  <button
                    type="submit"
                    className="send-button"
                    disabled={!newMessage.trim()}
                  >
                    发送
                  </button>
                </form>
              </div>
            </>
          ) : (
            <div className="no-conversation">
              <div className="no-conversation-content">
                <div className="no-conversation-icon">💬</div>
                <h3>选择一个会话开始聊天</h3>
                <p>从左侧选择一个会话，或者去好友页面开始新的聊天</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ChatPage
