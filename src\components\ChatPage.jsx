import { useState, useEffect, useRef } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useSocket } from '../hooks/useSocket'
import MessageList from './MessageList'
import MessageInput from './MessageInput'
import UserList from './UserList'
import './ChatPage.css'

const ChatPage = () => {
  const { user, logout } = useAuth()
  const [messages, setMessages] = useState([])
  const [onlineUsers, setOnlineUsers] = useState([])
  const [currentRoom, setCurrentRoom] = useState('general')
  const [showUserList, setShowUserList] = useState(false)
  const messagesEndRef = useRef(null)

  const socket = useSocket('http://localhost:5000', {
    auth: {
      token: localStorage.getItem('token')
    }
  })

  // 滚动到最新消息
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Socket事件监听
  useEffect(() => {
    if (!socket) return

    // 监听新消息
    socket.on('new_message', (message) => {
      setMessages(prev => [...prev, message])
    })

    // 监听用户加入
    socket.on('user_joined', (data) => {
      setOnlineUsers(prev => {
        const exists = prev.find(u => u.id === data.user.id)
        if (!exists) {
          return [...prev, data.user]
        }
        return prev
      })
    })

    // 监听用户离开
    socket.on('user_left', (data) => {
      setOnlineUsers(prev => prev.filter(u => u.id !== data.user.id))
    })

    // 连接成功后加载历史消息和在线用户
    socket.on('connect', () => {
      console.log('Socket连接成功')
      loadMessages()
      loadOnlineUsers()
    })

    socket.on('disconnect', () => {
      console.log('Socket连接断开')
    })

    return () => {
      socket.off('new_message')
      socket.off('user_joined')
      socket.off('user_left')
      socket.off('connect')
      socket.off('disconnect')
    }
  }, [socket])

  // 加载历史消息
  const loadMessages = async () => {
    try {
      const response = await fetch(`http://localhost:5000/api/messages?room=${currentRoom}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setMessages(data)
      }
    } catch (error) {
      console.error('加载消息失败:', error)
    }
  }

  // 加载在线用户
  const loadOnlineUsers = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/users', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setOnlineUsers(data)
      }
    } catch (error) {
      console.error('加载用户列表失败:', error)
    }
  }

  // 发送消息
  const sendMessage = (content) => {
    if (socket && content.trim()) {
      socket.emit('send_message', {
        content: content.trim(),
        room: currentRoom
      })
    }
  }

  // 处理登出
  const handleLogout = () => {
    if (socket) {
      socket.disconnect()
    }
    logout()
  }

  return (
    <div className="chat-page">
      {/* 头部导航 */}
      <header className="chat-header">
        <div className="header-left">
          <h1>GlesChat</h1>
          <span className="room-name">#{currentRoom}</span>
        </div>
        <div className="header-right">
          <button 
            className="users-btn"
            onClick={() => setShowUserList(!showUserList)}
          >
            <span className="users-count">{onlineUsers.length}</span>
            <span className="users-text">在线</span>
          </button>
          <div className="user-menu">
            <span className="username">{user?.username}</span>
            <button className="logout-btn" onClick={handleLogout}>
              退出
            </button>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <div className="chat-main">
        {/* 消息区域 */}
        <div className="messages-container">
          <MessageList 
            messages={messages} 
            currentUser={user}
          />
          <div ref={messagesEndRef} />
        </div>

        {/* 用户列表侧边栏 */}
        <div className={`users-sidebar ${showUserList ? 'show' : ''}`}>
          <UserList users={onlineUsers} />
        </div>
      </div>

      {/* 消息输入区域 */}
      <div className="message-input-container">
        <MessageInput onSendMessage={sendMessage} />
      </div>

      {/* 移动端用户列表遮罩 */}
      {showUserList && (
        <div 
          className="users-overlay"
          onClick={() => setShowUserList(false)}
        />
      )}
    </div>
  )
}

export default ChatPage
