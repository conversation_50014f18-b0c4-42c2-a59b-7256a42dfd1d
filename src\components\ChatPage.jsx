import { useState, useEffect, useRef } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useSocket } from '../hooks/useSocket'
import ConversationList from './ConversationList'
import MessageList from './MessageList'
import MessageInput from './MessageInput'
import FriendsList from './FriendsList'
import GroupsList from './GroupsList'
import './ChatPage.css'

const ChatPage = () => {
  const { user, logout } = useAuth()
  const [conversations, setConversations] = useState([])
  const [currentConversation, setCurrentConversation] = useState(null)
  const [messages, setMessages] = useState([])
  const [friends, setFriends] = useState([])
  const [groups, setGroups] = useState([])
  const [activeTab, setActiveTab] = useState('conversations') // conversations, friends, groups
  const [showSidebar, setShowSidebar] = useState(true)
  const messagesEndRef = useRef(null)

  const socket = useSocket('http://localhost:5000', {
    auth: {
      token: localStorage.getItem('token')
    }
  })

  // 滚动到最新消息
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // 初始化数据加载
  useEffect(() => {
    loadConversations()
    loadFriends()
    loadGroups()
  }, [])

  // Socket事件监听
  useEffect(() => {
    if (!socket) return

    // 监听新消息
    socket.on('new_message', (message) => {
      if (currentConversation && message.conversation_id === currentConversation.id) {
        setMessages(prev => [...prev, message])
      }
      // 更新会话列表中的最后消息
      setConversations(prev => prev.map(conv =>
        conv.id === message.conversation_id
          ? { ...conv, last_message: message, last_message_at: message.timestamp }
          : conv
      ))
    })

    // 监听好友上线/离线
    socket.on('friend_online', (data) => {
      setFriends(prev => prev.map(friend =>
        friend.id === data.user.id ? { ...friend, is_online: true } : friend
      ))
    })

    socket.on('friend_offline', (data) => {
      setFriends(prev => prev.map(friend =>
        friend.id === data.user.id ? { ...friend, is_online: false } : friend
      ))
    })

    // 监听打字状态
    socket.on('user_typing', (data) => {
      // 可以在这里处理打字状态显示
      console.log(`${data.user.username} is typing: ${data.is_typing}`)
    })

    socket.on('connect', () => {
      console.log('Socket连接成功')
    })

    socket.on('disconnect', () => {
      console.log('Socket连接断开')
    })

    return () => {
      socket.off('new_message')
      socket.off('friend_online')
      socket.off('friend_offline')
      socket.off('user_typing')
      socket.off('connect')
      socket.off('disconnect')
    }
  }, [socket, currentConversation])

  // 加载会话列表
  const loadConversations = async () => {
    try {
      const response = await fetch('/api/conversations', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setConversations(data)
      }
    } catch (error) {
      console.error('加载会话列表失败:', error)
    }
  }

  // 加载好友列表
  const loadFriends = async () => {
    try {
      const response = await fetch('/api/friends', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setFriends(data)
      }
    } catch (error) {
      console.error('加载好友列表失败:', error)
    }
  }

  // 加载群组列表
  const loadGroups = async () => {
    try {
      const response = await fetch('/api/groups', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setGroups(data)
      }
    } catch (error) {
      console.error('加载群组列表失败:', error)
    }
  }

  // 加载会话消息
  const loadConversationMessages = async (conversationId) => {
    try {
      const response = await fetch(`/api/conversations/${conversationId}/messages`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setMessages(data)
      }
    } catch (error) {
      console.error('加载消息失败:', error)
    }
  }

  // 选择会话
  const selectConversation = (conversation) => {
    setCurrentConversation(conversation)
    loadConversationMessages(conversation.id)

    // 加入会话房间
    if (socket) {
      socket.emit('join_conversation', {
        token: localStorage.getItem('token'),
        conversation_id: conversation.id
      })
    }
  }

  // 发送消息
  const sendMessage = (content) => {
    if (socket && content.trim() && currentConversation) {
      socket.emit('send_message', {
        token: localStorage.getItem('token'),
        conversation_id: currentConversation.id,
        content: content.trim()
      })
    }
  }

  // 处理登出
  const handleLogout = () => {
    if (socket) {
      socket.disconnect()
    }
    logout()
  }

  // 获取当前会话标题
  const getCurrentConversationTitle = () => {
    if (!currentConversation) return 'GlesChat'

    if (currentConversation.type === 'private') {
      return currentConversation.other_user?.username || '私聊'
    } else if (currentConversation.type === 'group') {
      return currentConversation.group?.name || '群聊'
    }
    return 'GlesChat'
  }

  return (
    <div className="chat-page">
      {/* 头部导航 */}
      <header className="chat-header">
        <div className="header-left">
          <button
            className="sidebar-toggle"
            onClick={() => setShowSidebar(!showSidebar)}
          >
            ☰
          </button>
          <h1>{getCurrentConversationTitle()}</h1>
        </div>
        <div className="header-right">
          <div className="user-menu">
            <span className="username">{user?.username}</span>
            <button
              className="admin-btn"
              onClick={() => window.open('http://localhost:5000/admin', '_blank')}
            >
              管理
            </button>
            <button className="logout-btn" onClick={handleLogout}>
              退出
            </button>
          </div>
        </div>
      </header>

      <div className="chat-main">
        {/* 侧边栏 */}
        <div className={`sidebar ${showSidebar ? 'show' : ''}`}>
          {/* 标签切换 */}
          <div className="sidebar-tabs">
            <button
              className={`tab ${activeTab === 'conversations' ? 'active' : ''}`}
              onClick={() => setActiveTab('conversations')}
            >
              💬 会话
            </button>
            <button
              className={`tab ${activeTab === 'friends' ? 'active' : ''}`}
              onClick={() => setActiveTab('friends')}
            >
              👥 好友
            </button>
            <button
              className={`tab ${activeTab === 'groups' ? 'active' : ''}`}
              onClick={() => setActiveTab('groups')}
            >
              🏢 群组
            </button>
          </div>

          {/* 内容区域 */}
          <div className="sidebar-content">
            {activeTab === 'conversations' && (
              <ConversationList
                conversations={conversations}
                currentConversation={currentConversation}
                onSelectConversation={selectConversation}
              />
            )}
            {activeTab === 'friends' && (
              <FriendsList
                friends={friends}
                onRefresh={loadFriends}
                onStartChat={(friend) => {
                  // 创建或打开私聊会话
                  // 这里需要实现创建私聊会话的逻辑
                }}
              />
            )}
            {activeTab === 'groups' && (
              <GroupsList
                groups={groups}
                onRefresh={loadGroups}
                onSelectGroup={(group) => {
                  // 找到对应的群组会话
                  const groupConv = conversations.find(conv =>
                    conv.type === 'group' && conv.group_id === group.id
                  )
                  if (groupConv) {
                    selectConversation(groupConv)
                  }
                }}
              />
            )}
          </div>
        </div>

        {/* 聊天区域 */}
        <div className="chat-area">
          {currentConversation ? (
            <>
              {/* 消息列表 */}
              <div className="messages-container">
                <MessageList
                  messages={messages}
                  currentUser={user}
                />
                <div ref={messagesEndRef} />
              </div>

              {/* 消息输入 */}
              <div className="message-input-container">
                <MessageInput onSendMessage={sendMessage} />
              </div>
            </>
          ) : (
            <div className="no-conversation">
              <div className="welcome-message">
                <h2>欢迎使用 GlesChat</h2>
                <p>选择一个会话开始聊天，或者添加好友创建新的对话</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 移动端侧边栏遮罩 */}
      {showSidebar && (
        <div
          className="sidebar-overlay"
          onClick={() => setShowSidebar(false)}
        />
      )}
    </div>
  )
}

export default ChatPage
