import { useState } from 'react'
import axios from 'axios'
import './FriendsList.css'

const FriendsList = ({ friends, onRefresh, onStartChat }) => {
  const [showAddFriend, setShowAddFriend] = useState(false)
  const [friendUsername, setFriendUsername] = useState('')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')

  const handleAddFriend = async (e) => {
    e.preventDefault()
    if (!friendUsername.trim()) return

    setLoading(true)
    setMessage('')

    try {
      const response = await axios.post('/api/friends/add', {
        username: friendUsername.trim()
      })
      
      setMessage('好友请求已发送')
      setFriendUsername('')
      setShowAddFriend(false)
      onRefresh()
    } catch (error) {
      setMessage(error.response?.data?.message || '添加好友失败')
    } finally {
      setLoading(false)
    }
  }

  const createPrivateChat = async (friend) => {
    try {
      const response = await axios.post('/api/conversations/private', {
        friend_id: friend.id
      })
      
      // 通知父组件切换到这个会话
      onStartChat(friend, response.data)
    } catch (error) {
      console.error('创建私聊失败:', error)
    }
  }

  return (
    <div className="friends-list">
      <div className="friends-header">
        <h3>好友列表 ({friends.length})</h3>
        <button 
          className="add-friend-btn"
          onClick={() => setShowAddFriend(!showAddFriend)}
        >
          ➕
        </button>
      </div>

      {message && (
        <div className={`message ${message.includes('失败') ? 'error' : 'success'}`}>
          {message}
        </div>
      )}

      {showAddFriend && (
        <div className="add-friend-form">
          <form onSubmit={handleAddFriend}>
            <input
              type="text"
              placeholder="输入用户名"
              value={friendUsername}
              onChange={(e) => setFriendUsername(e.target.value)}
              disabled={loading}
            />
            <div className="form-actions">
              <button type="submit" disabled={loading || !friendUsername.trim()}>
                {loading ? '发送中...' : '发送请求'}
              </button>
              <button 
                type="button" 
                onClick={() => setShowAddFriend(false)}
                disabled={loading}
              >
                取消
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="friends-content">
        {friends.length === 0 ? (
          <div className="empty-state">
            <p>暂无好友</p>
            <p>点击 ➕ 添加好友</p>
          </div>
        ) : (
          friends.map(friend => (
            <div key={friend.id} className="friend-item">
              <div className="friend-avatar">
                <div className="avatar">
                  {friend.username.charAt(0).toUpperCase()}
                </div>
                {friend.is_online && <div className="online-indicator"></div>}
              </div>
              
              <div className="friend-info">
                <h4 className="friend-name">{friend.username}</h4>
                <p className="friend-status">
                  {friend.is_online ? '在线' : '离线'}
                </p>
              </div>
              
              <div className="friend-actions">
                <button 
                  className="chat-btn"
                  onClick={() => createPrivateChat(friend)}
                  title="发起聊天"
                >
                  💬
                </button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}

export default FriendsList
