import { Outlet, useNavigate, useLocation } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import './MainLayout.css'

const MainLayout = () => {
  const { user, logout } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()

  const handleLogout = () => {
    logout()
    navigate('/')
  }

  const navItems = [
    { path: '/dashboard', label: '主页', icon: '🏠' },
    { path: '/friends', label: '好友', icon: '👥' },
    { path: '/chat', label: '聊天', icon: '💬' },
    { path: '/settings', label: '设置', icon: '⚙️' }
  ]

  const isActive = (path) => {
    if (path === '/chat') {
      return location.pathname.startsWith('/chat')
    }
    return location.pathname === path
  }

  return (
    <div className="main-layout">
      {/* 顶部导航栏 */}
      <header className="main-header">
        <div className="header-left">
          <h1 className="app-title">GlesChat</h1>
        </div>
        
        <nav className="main-nav">
          {navItems.map(item => (
            <button
              key={item.path}
              className={`nav-item ${isActive(item.path) ? 'active' : ''}`}
              onClick={() => navigate(item.path)}
            >
              <span className="nav-icon">{item.icon}</span>
              <span className="nav-label">{item.label}</span>
            </button>
          ))}
        </nav>

        <div className="header-right">
          <div className="user-info">
            <div className="user-avatar">
              {user?.username?.charAt(0).toUpperCase()}
            </div>
            <span className="username">{user?.username}</span>
          </div>
          
          <button 
            className="admin-btn"
            onClick={() => window.open('http://localhost:5000/admin', '_blank')}
            title="管理后台"
          >
            🛠️
          </button>
          
          <button className="logout-btn" onClick={handleLogout}>
            退出
          </button>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="main-content">
        <Outlet />
      </main>
    </div>
  )
}

export default MainLayout
