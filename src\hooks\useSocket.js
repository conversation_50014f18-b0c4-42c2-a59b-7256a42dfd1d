import { useEffect, useState } from 'react'
import { io } from 'socket.io-client'

export const useSocket = (url, options = {}) => {
  const [socket, setSocket] = useState(null)

  useEffect(() => {
    const token = localStorage.getItem('token')
    
    if (!token) {
      console.log('没有token，不建立socket连接')
      return
    }

    console.log('建立socket连接...')
    
    const socketInstance = io(url, {
      ...options,
      auth: {
        token,
        ...options.auth
      },
      transports: ['websocket', 'polling']
    })

    socketInstance.on('connect', () => {
      console.log('Socket连接成功，ID:', socketInstance.id)
    })

    socketInstance.on('connect_error', (error) => {
      console.error('Socket连接错误:', error)
    })

    socketInstance.on('disconnect', (reason) => {
      console.log('Socket断开连接:', reason)
    })

    setSocket(socketInstance)

    return () => {
      console.log('清理socket连接')
      socketInstance.disconnect()
    }
  }, [url])

  return socket
}
