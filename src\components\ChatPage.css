.chat-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.chat-header {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h1 {
  margin: 0;
  font-size: 1.5rem;
  color: #667eea;
  font-weight: 700;
}

.room-name {
  background: #667eea;
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.users-btn {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 20px;
  padding: 8px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
}

.users-btn:hover {
  background: #e9ecef;
}

.users-count {
  background: #28a745;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
}

.users-text {
  font-size: 0.9rem;
  color: #6c757d;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 10px;
}

.username {
  font-weight: 600;
  color: #495057;
}

.logout-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: #c82333;
}

.chat-main {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f8f9fa;
}

.users-sidebar {
  width: 280px;
  background: white;
  border-left: 1px solid #e9ecef;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 200;
}

.users-sidebar.show {
  transform: translateX(0);
}

.message-input-container {
  background: white;
  border-top: 1px solid #e9ecef;
  padding: 15px 20px;
}

.users-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 150;
  display: none;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .chat-header {
    padding: 10px 15px;
  }
  
  .header-left h1 {
    font-size: 1.3rem;
  }
  
  .room-name {
    font-size: 0.8rem;
    padding: 3px 10px;
  }
  
  .users-btn {
    padding: 6px 12px;
  }
  
  .username {
    display: none;
  }
  
  .logout-btn {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
  
  .messages-container {
    padding: 15px;
  }
  
  .message-input-container {
    padding: 10px 15px;
  }
  
  .users-sidebar {
    width: 100%;
  }
  
  .users-overlay {
    display: block;
  }
}

@media (max-width: 480px) {
  .chat-header {
    padding: 8px 12px;
  }
  
  .header-left {
    gap: 10px;
  }
  
  .header-left h1 {
    font-size: 1.2rem;
  }
  
  .header-right {
    gap: 10px;
  }
  
  .messages-container {
    padding: 10px;
  }
  
  .message-input-container {
    padding: 8px 12px;
  }
}
