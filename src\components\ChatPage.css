.chat-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.chat-header {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.sidebar-toggle {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.sidebar-toggle:hover {
  background: #e9ecef;
}

.header-left h1 {
  margin: 0;
  font-size: 1.5rem;
  color: #667eea;
  font-weight: 700;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.users-btn {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 20px;
  padding: 8px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
}

.users-btn:hover {
  background: #e9ecef;
}

.users-count {
  background: #28a745;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
}

.users-text {
  font-size: 0.9rem;
  color: #6c757d;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 10px;
}

.username {
  font-weight: 600;
  color: #495057;
}

.admin-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  margin-right: 10px;
}

.admin-btn:hover {
  background: #218838;
}

.logout-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: #c82333;
}

.chat-main {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
}

.sidebar {
  width: 320px;
  background: white;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
}

.sidebar-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.sidebar-tabs .tab {
  flex: 1;
  padding: 12px 8px;
  border: none;
  background: transparent;
  cursor: pointer;
  font-size: 0.85rem;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.3s ease;
  border-bottom: 2px solid transparent;
}

.sidebar-tabs .tab:hover {
  background: #e9ecef;
  color: #495057;
}

.sidebar-tabs .tab.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: white;
}

.sidebar-content {
  flex: 1;
  overflow: hidden;
}

.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.message-input-container {
  background: white;
  border-top: 1px solid #e9ecef;
  padding: 15px 20px;
}

.no-conversation {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
}

.welcome-message {
  text-align: center;
  color: #6c757d;
  max-width: 400px;
  padding: 40px 20px;
}

.welcome-message h2 {
  margin: 0 0 16px 0;
  color: #495057;
  font-size: 1.5rem;
}

.welcome-message p {
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
}

.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 150;
  display: none;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .chat-header {
    padding: 10px 15px;
  }

  .header-left {
    gap: 10px;
  }

  .sidebar-toggle {
    padding: 6px 10px;
    font-size: 1rem;
  }

  .header-left h1 {
    font-size: 1.3rem;
  }

  .username {
    display: none;
  }

  .admin-btn {
    display: none;
  }

  .logout-btn {
    padding: 6px 12px;
    font-size: 0.8rem;
  }

  .sidebar {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 200;
    transform: translateX(-100%);
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .sidebar-tabs .tab {
    padding: 10px 6px;
    font-size: 0.8rem;
  }

  .messages-container {
    padding: 15px;
  }

  .message-input-container {
    padding: 10px 15px;
  }

  .sidebar-overlay {
    display: block;
  }

  .welcome-message {
    padding: 20px;
  }

  .welcome-message h2 {
    font-size: 1.3rem;
  }

  .welcome-message p {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .chat-header {
    padding: 8px 12px;
  }

  .header-left {
    gap: 10px;
  }

  .header-left h1 {
    font-size: 1.2rem;
  }

  .header-right {
    gap: 10px;
  }

  .messages-container {
    padding: 10px;
  }

  .message-input-container {
    padding: 8px 12px;
  }
}
