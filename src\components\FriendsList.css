.friends-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.friends-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.friends-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #495057;
}

.add-friend-btn {
  background: #667eea;
  color: white;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.add-friend-btn:hover {
  background: #5a6fd8;
  transform: scale(1.1);
}

.message {
  padding: 8px 16px;
  margin: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
}

.message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.add-friend-form {
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.add-friend-form input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.add-friend-form input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.form-actions {
  display: flex;
  gap: 8px;
}

.form-actions button {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.form-actions button[type="submit"] {
  background: #667eea;
  color: white;
}

.form-actions button[type="submit"]:hover:not(:disabled) {
  background: #5a6fd8;
}

.form-actions button[type="button"] {
  background: #6c757d;
  color: white;
}

.form-actions button[type="button"]:hover:not(:disabled) {
  background: #5a6268;
}

.form-actions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.friends-content {
  flex: 1;
  overflow-y: auto;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
  color: #6c757d;
}

.empty-state p {
  margin: 5px 0;
  font-size: 0.9rem;
}

.friend-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.friend-item:hover {
  background-color: #f8f9fa;
}

.friend-avatar {
  position: relative;
  margin-right: 12px;
  flex-shrink: 0;
}

.friend-avatar .avatar {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
}

.online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #28a745;
  border: 2px solid white;
  border-radius: 50%;
}

.friend-info {
  flex: 1;
  min-width: 0;
}

.friend-name {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.friend-status {
  font-size: 0.85rem;
  color: #6c757d;
  margin: 0;
}

.friend-actions {
  display: flex;
  gap: 8px;
}

.chat-btn {
  background: #28a745;
  color: white;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.chat-btn:hover {
  background: #218838;
  transform: scale(1.1);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .friends-header {
    padding: 12px;
  }
  
  .add-friend-form {
    padding: 12px;
  }
  
  .friend-item {
    padding: 10px 12px;
  }
  
  .friend-avatar {
    margin-right: 10px;
  }
  
  .friend-avatar .avatar {
    width: 38px;
    height: 38px;
    font-size: 0.9rem;
  }
  
  .chat-btn {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .friends-header h3 {
    font-size: 1rem;
  }
  
  .add-friend-btn {
    width: 28px;
    height: 28px;
    font-size: 1rem;
  }
  
  .friend-item {
    padding: 8px 10px;
  }
  
  .friend-name {
    font-size: 0.95rem;
  }
  
  .friend-status {
    font-size: 0.8rem;
  }
}
